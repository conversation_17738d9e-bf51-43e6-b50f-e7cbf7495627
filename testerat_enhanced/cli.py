#!/usr/bin/env python3
"""
Enhanced Testerat Command Line Interface

Universal web testing framework that works with any web application.
Provides comprehensive testing with actionable insights and fix recommendations.

Usage:
    python -m testerat_enhanced.cli https://example.com
    python -m testerat_enhanced.cli http://localhost:3000 "FAAFO Career Platform Testing"
"""

import sys
import argparse
import json
from pathlib import Path

from .core.testerat_enhanced import EnhancedTesterat
from .config.test_config import UniversalTestConfig, FrameworkType


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="🎯 Enhanced Testerat - Universal Web Testing Framework",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Test any web application
  python -m testerat_enhanced.cli https://example.com
  
  # Test local development server
  python -m testerat_enhanced.cli http://localhost:3000
  
  # Test with custom description
  python -m testerat_enhanced.cli https://myapp.com "My App Testing"
  
  # Test with specific framework optimization
  python -m testerat_enhanced.cli https://myapp.com --framework react
  
  # Test with custom configuration
  python -m testerat_enhanced.cli https://myapp.com --config config.json
  
  # Headless mode (default)
  python -m testerat_enhanced.cli https://myapp.com --headless
  
  # Visible browser mode
  python -m testerat_enhanced.cli https://myapp.com --no-headless

Universal Support:
  ✅ React, Vue, Angular, Next.js, Nuxt.js, Svelte
  ✅ Any authentication system (NextAuth, Auth0, custom)
  ✅ Any API backend (Node.js, Python, PHP, etc.)
  ✅ Any deployment environment (local, staging, production)

Critical Issue Detection:
  🚨 Authentication state bugs (useSession loading issues)
  🚨 Workflow navigation failures (duplicate case statements)
  🚨 CSRF token header mismatches (X-CSRF-Token vs x-csrf-token)
  🚨 API endpoint failures and network errors
  🚨 Security vulnerabilities and accessibility issues
        """
    )
    
    # Required arguments
    parser.add_argument(
        'url',
        help='URL of the web application to test'
    )
    
    parser.add_argument(
        'description',
        nargs='?',
        default='Comprehensive Testing',
        help='Description of the test session (default: "Comprehensive Testing")'
    )
    
    # Optional arguments
    parser.add_argument(
        '--framework',
        choices=['react', 'vue', 'angular', 'nextjs', 'nuxtjs', 'svelte', 'auto'],
        default='auto',
        help='Target framework for optimization (default: auto-detect)'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='Path to custom configuration JSON file'
    )
    
    parser.add_argument(
        '--headless',
        action='store_true',
        default=True,
        help='Run in headless mode (default)'
    )
    
    parser.add_argument(
        '--no-headless',
        action='store_true',
        help='Run with visible browser'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default='testerat_reports',
        help='Output directory for reports (default: testerat_reports)'
    )
    
    parser.add_argument(
        '--skip-auth',
        action='store_true',
        help='Skip authentication testing'
    )
    
    parser.add_argument(
        '--skip-workflows',
        action='store_true',
        help='Skip workflow testing'
    )
    
    parser.add_argument(
        '--skip-api',
        action='store_true',
        help='Skip API testing'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='Enhanced Testerat 2.0.0'
    )
    
    args = parser.parse_args()
    
    # Handle headless mode
    headless = args.headless and not args.no_headless
    
    # Create configuration
    config = UniversalTestConfig()
    
    # Load custom configuration if provided
    if args.config:
        try:
            with open(args.config, 'r') as f:
                custom_config = json.load(f)
                # Apply custom configuration
                for key, value in custom_config.items():
                    if hasattr(config, key):
                        setattr(config, key, value)
            print(f"✅ Loaded custom configuration from {args.config}")
        except Exception as e:
            print(f"❌ Failed to load configuration: {e}")
            sys.exit(1)
    
    # Apply CLI arguments to configuration
    config.headless = headless
    config.detailed_logging = args.verbose
    config.test_authentication = not args.skip_auth
    config.test_workflows = not args.skip_workflows
    config.test_api_interactions = not args.skip_api
    
    # Set framework if specified
    if args.framework != 'auto':
        framework_map = {
            'react': FrameworkType.REACT,
            'vue': FrameworkType.VUE,
            'angular': FrameworkType.ANGULAR,
            'nextjs': FrameworkType.NEXTJS,
            'nuxtjs': FrameworkType.NUXTJS,
            'svelte': FrameworkType.SVELTE
        }
        config.framework = framework_map[args.framework]
    
    # Print startup information
    print("🎯 Enhanced Testerat - Universal Web Testing Framework")
    print("=" * 60)
    print(f"🌐 Target URL: {args.url}")
    print(f"📝 Description: {args.description}")
    print(f"🖥️  Browser Mode: {'Headless' if headless else 'Visible'}")
    print(f"🔧 Framework: {args.framework}")
    print(f"📊 Output Directory: {args.output_dir}")
    print()
    
    # Show test scope
    test_scope = []
    if config.test_authentication:
        test_scope.append("🔐 Authentication")
    if config.test_workflows:
        test_scope.append("🔄 Workflows")
    if config.test_api_interactions:
        test_scope.append("🌐 API")
    if config.test_security:
        test_scope.append("🛡️ Security")
    if config.test_accessibility:
        test_scope.append("♿ Accessibility")
    if config.test_performance:
        test_scope.append("⚡ Performance")
    
    print(f"🎯 Test Scope: {', '.join(test_scope)}")
    print()
    
    try:
        # Initialize Enhanced Testerat
        testerat = EnhancedTesterat(config)
        
        # Run comprehensive testing
        print("🚀 Starting comprehensive testing...")
        results = testerat.run_comprehensive_test(args.url, args.description)
        
        # Print results summary
        print()
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 40)
        
        summary = results['summary']
        print(f"✅ Total Tests: {summary['total_tests']}")
        print(f"✅ Passed: {summary['passed']}")
        print(f"❌ Failed: {summary['failed']}")
        print(f"🚨 Critical Issues: {len(results['critical_issues'])}")
        print(f"📈 Success Rate: {summary['success_rate']:.1f}%")
        print(f"⏱️  Execution Time: {summary['total_execution_time']:.2f}s")
        print()
        
        # Show critical issues
        if results['critical_issues']:
            print("🚨 CRITICAL ISSUES FOUND")
            print("=" * 30)
            for issue in results['critical_issues']:
                print(f"❌ {issue['test_name'].replace('_', ' ').title()}")
                print(f"   Problem: {issue['details']}")
                print(f"   Severity: {issue['severity']}")
                if issue['recommendations']:
                    print(f"   Fix: {issue['recommendations'][0]}")
                print()
        else:
            print("🎉 NO CRITICAL ISSUES FOUND!")
            print("Your application passed all critical tests.")
            print()
        
        # Show top recommendations
        if results['recommendations']:
            print("🔧 TOP RECOMMENDATIONS")
            print("=" * 25)
            for i, rec in enumerate(results['recommendations'][:5], 1):
                print(f"{i}. {rec}")
            print()
        
        # Show generated reports
        print("📄 GENERATED REPORTS")
        print("=" * 20)
        for report_type, file_path in results['report_files'].items():
            if file_path:
                print(f"📊 {report_type.upper()}: {file_path}")
        print()
        
        # Final status
        if len(results['critical_issues']) > 0:
            print("🚨 TESTING COMPLETED WITH CRITICAL ISSUES")
            print("Please review the critical issues and implement the recommended fixes.")
            sys.exit(1)
        elif summary['failed'] > 0:
            print("⚠️  TESTING COMPLETED WITH ISSUES")
            print("Please review the failed tests and consider implementing improvements.")
            sys.exit(0)
        else:
            print("✅ TESTING COMPLETED SUCCESSFULLY")
            print("Your application passed all tests!")
            sys.exit(0)
            
    except KeyboardInterrupt:
        print("\n❌ Testing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Testing failed: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
