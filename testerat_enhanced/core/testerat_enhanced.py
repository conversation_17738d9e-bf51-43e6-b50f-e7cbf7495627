"""
Enhanced Testerat - Universal Web Testing Framework

Main orchestrator that integrates all testing engines to provide comprehensive
web application testing across any framework and technology stack.

Universal Support:
- Any web application framework (React, Vue, Angular, vanilla JS)
- Any authentication system (NextAuth, Auth0, custom, etc.)
- Any API backend (Node.js, Python, PHP, etc.)
- Any deployment environment (local, staging, production)

Key Features:
- Authentication Engine: Tests authenticated user experiences
- Workflow Engine: Tests complete user journeys and multi-step flows
- API Engine: Tests real form submissions and API interactions
- Enhanced Reporting: Actionable insights with fix recommendations
"""

import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from playwright.sync_api import sync_playwright, <PERSON>, Browser, BrowserContext

from ..config.test_config import UniversalTestConfig, FrameworkType, create_config_for_framework
from ..engines.authentication import AuthenticationEngine
from ..engines.workflow import WorkflowEngine
from ..engines.api import APIEngine
from ..reporting.enhanced_reporter import EnhancedReporter, ReportConfig
from ..utils.test_result import TestSuite, TestResult, TestCategory


@dataclass
class TestSession:
    """Universal test session information"""
    url: str
    framework: FrameworkType = FrameworkType.AUTO_DETECT
    app_name: str = "Unknown Application"
    test_description: str = "Comprehensive Testing"
    start_time: float = 0.0
    end_time: float = 0.0


class EnhancedTesterat:
    """
    Enhanced Testerat - Universal Web Testing Framework
    
    Main orchestrator that runs comprehensive testing across any web application.
    Integrates authentication, workflow, and API testing engines with enhanced reporting.
    """
    
    def __init__(self, config: UniversalTestConfig = None):
        self.config = config or UniversalTestConfig()
        self.logger = self._setup_logging()
        
        # Initialize Playwright
        self.playwright = None
        self.browser = None
        self.context = None
        
        # Initialize testing engines
        self.auth_engine = AuthenticationEngine(self.config.auth_config, self.logger)
        self.workflow_engine = WorkflowEngine(self.config.workflow_config, self.logger)
        self.api_engine = APIEngine(self.config.api_config, self.logger)
        
        # Initialize reporter
        report_config = ReportConfig()
        self.reporter = EnhancedReporter(report_config, self.logger)
        
        # Test session tracking
        self.current_session: Optional[TestSession] = None
        self.test_suite: Optional[TestSuite] = None
    
    def run_comprehensive_test(self, url: str, test_description: str = "Comprehensive Testing") -> Dict[str, Any]:
        """
        Run comprehensive testing suite on any web application
        
        Universal testing approach that works with any framework:
        1. Framework detection and optimization
        2. Authentication testing
        3. Workflow testing
        4. API testing
        5. Enhanced reporting
        
        Returns comprehensive test results and report file paths
        """
        self.logger.info(f"🎯 Starting Enhanced Testerat for: {url}")
        
        # Initialize test session
        self.current_session = TestSession(
            url=url,
            test_description=test_description,
            start_time=time.time()
        )
        
        self.test_suite = TestSuite(name=f"Enhanced Testerat - {url}")
        
        try:
            # Setup browser environment
            self._setup_browser()
            
            # Create page and navigate
            page = self.context.new_page()
            page.goto(url, wait_until='networkidle', timeout=self.config.timeout)
            
            # Phase 1: Framework Detection and Optimization
            framework_info = self._detect_and_optimize_framework(page)
            self.current_session.framework = framework_info['framework']
            self.current_session.app_name = framework_info.get('app_name', 'Unknown Application')
            
            # Phase 2: Authentication Testing
            if self.config.test_authentication:
                self.logger.info("🔐 Running Authentication Tests")
                auth_results = self.auth_engine.run_comprehensive_auth_tests(page, url)
                for result in auth_results:
                    result.framework_detected = framework_info['framework'].value
                    self.test_suite.add_result(result)
            
            # Phase 3: Workflow Testing
            if self.config.test_workflows:
                self.logger.info("🔄 Running Workflow Tests")
                workflow_results = self.workflow_engine.run_comprehensive_workflow_tests(page)
                for result in workflow_results:
                    result.framework_detected = framework_info['framework'].value
                    self.test_suite.add_result(result)
            
            # Phase 4: API Testing
            if self.config.test_api_interactions:
                self.logger.info("🌐 Running API Tests")
                api_results = self.api_engine.run_comprehensive_api_tests(page, url)
                for result in api_results:
                    result.framework_detected = framework_info['framework'].value
                    self.test_suite.add_result(result)
            
            # Phase 5: Additional Testing (Security, Accessibility, Performance)
            if self.config.test_security or self.config.test_accessibility or self.config.test_performance:
                additional_results = self._run_additional_tests(page)
                for result in additional_results:
                    result.framework_detected = framework_info['framework'].value
                    self.test_suite.add_result(result)
            
            # Finalize session
            self.current_session.end_time = time.time()
            self.test_suite.end_time = self.current_session.end_time
            self.test_suite.total_execution_time = self.current_session.end_time - self.current_session.start_time
            
            # Phase 6: Generate Enhanced Reports
            self.logger.info("📊 Generating Enhanced Reports")
            report_files = self.reporter.generate_comprehensive_report(
                self.test_suite,
                framework_info,
                {'name': self.current_session.app_name, 'url': url}
            )
            
            # Cleanup
            page.close()
            self._cleanup_browser()
            
            # Return comprehensive results
            return {
                'session': {
                    'url': url,
                    'framework': framework_info['framework'].value,
                    'app_name': self.current_session.app_name,
                    'execution_time': self.test_suite.total_execution_time
                },
                'summary': self.test_suite.get_summary(),
                'critical_issues': [result.to_dict() for result in self.test_suite.get_critical_issues()],
                'all_results': [result.to_dict() for result in self.test_suite.results],
                'report_files': report_files,
                'recommendations': self._get_top_recommendations()
            }
            
        except Exception as e:
            self.logger.error(f"Testing failed: {str(e)}")
            self._cleanup_browser()
            raise
    
    def _setup_browser(self):
        """Setup Playwright browser environment"""
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.launch(
            headless=self.config.headless,
            args=['--disable-web-security', '--disable-features=VizDisplayCompositor']
        )
        self.context = self.browser.new_context(
            viewport={'width': self.config.viewport_width, 'height': self.config.viewport_height}
        )
    
    def _cleanup_browser(self):
        """Cleanup browser resources"""
        if self.context:
            self.context.close()
        if self.browser:
            self.browser.close()
        if self.playwright:
            self.playwright.stop()
    
    def _detect_and_optimize_framework(self, page: Page) -> Dict[str, Any]:
        """
        Universal framework detection and optimization
        
        Detects the web framework and optimizes testing configuration accordingly
        """
        self.logger.info("🔍 Detecting web framework")
        
        framework_info = {
            'framework': FrameworkType.VANILLA_JS,
            'app_name': 'Unknown Application',
            'version': None,
            'features': []
        }
        
        try:
            # Get page content for analysis
            page_content = page.content()
            page_title = page.title()
            
            # Detect framework based on indicators
            for framework, indicators in self.config.framework_indicators.items():
                for indicator in indicators:
                    if indicator in page_content or page.query_selector(indicator):
                        framework_info['framework'] = framework
                        self.logger.info(f"Detected framework: {framework.value}")
                        break
                if framework_info['framework'] != FrameworkType.VANILLA_JS:
                    break
            
            # Extract app name from title or meta tags
            if page_title:
                framework_info['app_name'] = page_title
            
            # Detect specific features
            features = []
            if page.query_selector('[data-testid]'):
                features.append('test-ids')
            if page.query_selector('.loading, .spinner'):
                features.append('loading-states')
            if page.query_selector('form'):
                features.append('forms')
            if page.query_selector('[role="button"], button'):
                features.append('interactive-elements')
            
            framework_info['features'] = features
            
            # Optimize configuration for detected framework
            if framework_info['framework'] != FrameworkType.AUTO_DETECT:
                optimized_config = create_config_for_framework(
                    framework_info['framework'], 
                    page.url
                )
                # Update current config with optimizations
                self.config.auth_config = optimized_config.auth_config
                self.config.workflow_config = optimized_config.workflow_config
                self.config.api_config = optimized_config.api_config
            
        except Exception as e:
            self.logger.warning(f"Framework detection failed: {e}")
        
        return framework_info
    
    def _run_additional_tests(self, page: Page) -> List[TestResult]:
        """Run additional tests (security, accessibility, performance)"""
        additional_results = []
        
        # Basic security tests
        if self.config.test_security:
            security_result = self._run_basic_security_tests(page)
            if security_result:
                additional_results.append(security_result)
        
        # Basic accessibility tests
        if self.config.test_accessibility:
            accessibility_result = self._run_basic_accessibility_tests(page)
            if accessibility_result:
                additional_results.append(accessibility_result)
        
        # Basic performance tests
        if self.config.test_performance:
            performance_result = self._run_basic_performance_tests(page)
            if performance_result:
                additional_results.append(performance_result)
        
        return additional_results
    
    def _run_basic_security_tests(self, page: Page) -> Optional[TestResult]:
        """Run basic security tests"""
        issues = []
        recommendations = []
        
        try:
            # Check for HTTPS
            if not page.url.startswith('https://') and not page.url.startswith('http://localhost'):
                issues.append("Application not using HTTPS")
                recommendations.append("Implement HTTPS for secure communication")
            
            # Check for security headers (basic check)
            # This would require more advanced implementation
            
        except Exception as e:
            issues.append(f"Security test failed: {str(e)}")
        
        if issues:
            return TestResult(
                test_name="basic_security",
                status="FAILED",
                details="; ".join(issues),
                severity="MEDIUM",
                recommendations=recommendations,
                category=TestCategory.SECURITY.value
            )
        
        return None
    
    def _run_basic_accessibility_tests(self, page: Page) -> Optional[TestResult]:
        """Run basic accessibility tests"""
        issues = []
        recommendations = []
        
        try:
            # Check for images without alt text
            images_without_alt = page.query_selector_all('img:not([alt])')
            if images_without_alt:
                issues.append(f"{len(images_without_alt)} images missing alt text")
                recommendations.append("Add alt attributes to all images")
            
            # Check for form labels
            inputs_without_labels = page.query_selector_all('input:not([aria-label]):not([aria-labelledby])')
            unlabeled_count = 0
            for inp in inputs_without_labels:
                input_id = inp.get_attribute('id')
                if not input_id or not page.query_selector(f'label[for="{input_id}"]'):
                    unlabeled_count += 1
            
            if unlabeled_count > 0:
                issues.append(f"{unlabeled_count} form inputs without proper labels")
                recommendations.append("Associate labels with form inputs")
            
        except Exception as e:
            issues.append(f"Accessibility test failed: {str(e)}")
        
        if issues:
            return TestResult(
                test_name="basic_accessibility",
                status="FAILED",
                details="; ".join(issues),
                severity="MEDIUM",
                recommendations=recommendations,
                category=TestCategory.ACCESSIBILITY.value
            )
        
        return None
    
    def _run_basic_performance_tests(self, page: Page) -> Optional[TestResult]:
        """Run basic performance tests"""
        issues = []
        recommendations = []
        
        try:
            # Check page load time (basic)
            # This would require more sophisticated performance monitoring
            pass
            
        except Exception as e:
            issues.append(f"Performance test failed: {str(e)}")
        
        return None
    
    def _get_top_recommendations(self) -> List[str]:
        """Get top recommendations from all test results"""
        all_recommendations = set()
        for result in self.test_suite.results:
            all_recommendations.update(result.recommendations)
        
        # Prioritize critical recommendations
        critical_recs = [rec for rec in all_recommendations if 'critical' in rec.lower() or 'fix' in rec.lower()]
        other_recs = [rec for rec in all_recommendations if rec not in critical_recs]
        
        return list(critical_recs)[:5] + list(other_recs)[:10]
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO if self.config.detailed_logging else logging.WARNING,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('testerat_enhanced.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
