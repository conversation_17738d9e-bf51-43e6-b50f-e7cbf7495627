"""
🎯 testerat Enhanced - Universal Web Testing Framework

A comprehensive, AI-powered web testing framework that catches real production issues
through authentication testing, workflow validation, and API interaction testing.

Universal Design:
- Works with any web application (React, Vue, Angular, vanilla JS)
- Tests any authentication system (NextAuth, Auth0, custom, etc.)
- Validates any API backend (Node.js, Python, PHP, etc.)
- Supports any deployment environment (local, staging, production)

Key Features:
- Authentication Engine: Tests authenticated user experiences
- Workflow Engine: Tests complete user journeys and multi-step flows
- API Engine: Tests real form submissions and API interactions
- Enhanced Reporting: Actionable insights with fix recommendations
- Universal Compatibility: Framework-agnostic testing approach

Battle-tested against real production issues to ensure reliability.
"""

from .core.testerat_enhanced import EnhancedTesterat
from .engines.authentication import AuthenticationEngine
from .engines.workflow import WorkflowEngine
from .engines.api import APIEngine
from .reporting.enhanced_reporter import EnhancedReporter
from .config.test_config import UniversalTestConfig

__version__ = "2.0.0"
__author__ = "testerat Enhanced Team"

# Universal exports for any web application
__all__ = [
    'EnhancedTesterat',
    'AuthenticationEngine', 
    'WorkflowEngine',
    'APIEngine',
    'EnhancedReporter',
    'UniversalTestConfig'
]
