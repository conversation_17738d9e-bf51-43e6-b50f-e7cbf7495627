"""
Enhanced Reporting System for testerat Enhanced

Generates actionable reports with specific fix recommendations and code location mapping.
Provides comprehensive analysis with visual reports and export capabilities.

Universal Support:
- Any web application framework
- Multiple export formats (HTML, JSON, PDF)
- Actionable insights with fix examples
- Code location mapping for debugging
"""

import json
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

from ..utils.test_result import TestResult, TestSuite, TestSeverity, TestStatus, TestCategory


@dataclass
class ReportConfig:
    """Configuration for report generation"""
    output_dir: str = "testerat_reports"
    include_screenshots: bool = True
    include_fix_examples: bool = True
    include_code_locations: bool = True
    generate_html: bool = True
    generate_json: bool = True
    generate_pdf: bool = False
    theme: str = "modern"  # modern, classic, minimal


class EnhancedReporter:
    """
    Enhanced Reporting System
    
    Generates comprehensive, actionable reports with:
    - Issue classification by severity
    - Specific fix recommendations
    - Code location mapping
    - Visual charts and graphs
    - Multiple export formats
    """
    
    def __init__(self, config: ReportConfig = None, logger: logging.Logger = None):
        self.config = config or ReportConfig()
        self.logger = logger or logging.getLogger(__name__)
        self.report_timestamp = datetime.now()
        
        # Create output directory
        Path(self.config.output_dir).mkdir(parents=True, exist_ok=True)
    
    def generate_comprehensive_report(self, test_suite: TestSuite, 
                                    framework_info: Dict[str, Any] = None,
                                    app_info: Dict[str, Any] = None) -> Dict[str, str]:
        """
        Generate comprehensive report in multiple formats
        
        Returns dictionary with file paths for each generated format
        """
        self.logger.info("🎯 Generating comprehensive testerat report")
        
        generated_files = {}
        
        # Generate HTML report
        if self.config.generate_html:
            html_file = self._generate_html_report(test_suite, framework_info, app_info)
            generated_files['html'] = html_file
        
        # Generate JSON report
        if self.config.generate_json:
            json_file = self._generate_json_report(test_suite, framework_info, app_info)
            generated_files['json'] = json_file
        
        # Generate PDF report (if requested)
        if self.config.generate_pdf:
            pdf_file = self._generate_pdf_report(test_suite, framework_info, app_info)
            generated_files['pdf'] = pdf_file
        
        # Generate summary report
        summary_file = self._generate_summary_report(test_suite)
        generated_files['summary'] = summary_file
        
        self.logger.info(f"Reports generated: {list(generated_files.keys())}")
        return generated_files
    
    def _generate_html_report(self, test_suite: TestSuite, 
                             framework_info: Dict[str, Any] = None,
                             app_info: Dict[str, Any] = None) -> str:
        """Generate comprehensive HTML report"""
        
        timestamp_str = self.report_timestamp.strftime("%Y%m%d_%H%M%S")
        filename = f"{self.config.output_dir}/testerat_report_{timestamp_str}.html"
        
        # Generate report sections
        summary_section = self._generate_summary_section(test_suite)
        critical_issues_section = self._generate_critical_issues_section(test_suite)
        detailed_results_section = self._generate_detailed_results_section(test_suite)
        fix_recommendations_section = self._generate_fix_recommendations_section(test_suite)
        charts_section = self._generate_charts_section(test_suite)
        
        # HTML template
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>testerat Enhanced Report - {test_suite.name}</title>
    <style>
        {self._get_css_styles()}
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header class="report-header">
            <h1>🎯 testerat Enhanced Report</h1>
            <div class="report-meta">
                <p><strong>Test Suite:</strong> {test_suite.name}</p>
                <p><strong>Generated:</strong> {self.report_timestamp.strftime("%Y-%m-%d %H:%M:%S")}</p>
                {f'<p><strong>Framework:</strong> {framework_info.get("framework", "Unknown")}</p>' if framework_info else ''}
                {f'<p><strong>Application:</strong> {app_info.get("name", "Unknown")}</p>' if app_info else ''}
            </div>
        </header>
        
        {summary_section}
        {critical_issues_section}
        {charts_section}
        {fix_recommendations_section}
        {detailed_results_section}
        
        <footer class="report-footer">
            <p>Generated by testerat Enhanced - Universal Web Testing Framework</p>
            <p>Report ID: {timestamp_str}</p>
        </footer>
    </div>
    
    <script>
        {self._get_javascript()}
    </script>
</body>
</html>
        """
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return filename
    
    def _generate_json_report(self, test_suite: TestSuite,
                             framework_info: Dict[str, Any] = None,
                             app_info: Dict[str, Any] = None) -> str:
        """Generate JSON report for automation"""
        
        timestamp_str = self.report_timestamp.strftime("%Y%m%d_%H%M%S")
        filename = f"{self.config.output_dir}/testerat_report_{timestamp_str}.json"
        
        # Prepare comprehensive JSON data
        report_data = {
            'metadata': {
                'report_id': timestamp_str,
                'generated_at': self.report_timestamp.isoformat(),
                'testerat_version': '2.0.0',
                'framework_info': framework_info or {},
                'app_info': app_info or {}
            },
            'summary': test_suite.get_summary(),
            'test_results': [result.to_dict() for result in test_suite.results],
            'critical_issues': [result.to_dict() for result in test_suite.get_critical_issues()],
            'failures': [result.to_dict() for result in test_suite.get_failures()],
            'recommendations': self._extract_all_recommendations(test_suite),
            'fix_examples': self._extract_all_fix_examples(test_suite),
            'code_locations': self._extract_code_locations(test_suite),
            'statistics': self._generate_statistics(test_suite)
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        return filename
    
    def _generate_summary_section(self, test_suite: TestSuite) -> str:
        """Generate summary section HTML"""
        summary = test_suite.get_summary()
        critical_count = len(test_suite.get_critical_issues())
        
        status_class = "success" if summary['failed'] == 0 else "warning" if critical_count == 0 else "critical"
        
        return f"""
        <section class="summary-section">
            <h2>📊 Test Summary</h2>
            <div class="summary-grid">
                <div class="summary-card {status_class}">
                    <h3>Overall Status</h3>
                    <div class="status-indicator {status_class}">
                        {'✅ PASSED' if summary['failed'] == 0 else '🚨 CRITICAL' if critical_count > 0 else '⚠️ ISSUES FOUND'}
                    </div>
                </div>
                <div class="summary-card">
                    <h3>Test Results</h3>
                    <div class="test-stats">
                        <div class="stat">
                            <span class="stat-number">{summary['total_tests']}</span>
                            <span class="stat-label">Total Tests</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number passed">{summary['passed']}</span>
                            <span class="stat-label">Passed</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number failed">{summary['failed']}</span>
                            <span class="stat-label">Failed</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number critical">{critical_count}</span>
                            <span class="stat-label">Critical</span>
                        </div>
                    </div>
                </div>
                <div class="summary-card">
                    <h3>Success Rate</h3>
                    <div class="success-rate">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {summary['success_rate']:.1f}%"></div>
                        </div>
                        <span class="percentage">{summary['success_rate']:.1f}%</span>
                    </div>
                </div>
                <div class="summary-card">
                    <h3>Execution Time</h3>
                    <div class="execution-time">
                        <span class="time-value">{summary['total_execution_time']:.2f}s</span>
                    </div>
                </div>
            </div>
        </section>
        """
    
    def _generate_critical_issues_section(self, test_suite: TestSuite) -> str:
        """Generate critical issues section"""
        critical_issues = test_suite.get_critical_issues()
        
        if not critical_issues:
            return """
            <section class="critical-issues-section">
                <h2>🎉 No Critical Issues Found</h2>
                <div class="no-issues">
                    <p>Excellent! No critical issues were detected in your application.</p>
                </div>
            </section>
            """
        
        issues_html = ""
        for issue in critical_issues:
            issues_html += f"""
            <div class="critical-issue">
                <div class="issue-header">
                    <h4>🚨 {issue.test_name.replace('_', ' ').title()}</h4>
                    <span class="severity-badge critical">{issue.severity}</span>
                </div>
                <div class="issue-details">
                    <p><strong>Problem:</strong> {issue.details}</p>
                    {f'<p><strong>Code Location:</strong> <code>{issue.code_location}</code></p>' if issue.code_location else ''}
                </div>
                <div class="issue-recommendations">
                    <h5>🔧 Fix Recommendations:</h5>
                    <ul>
                        {''.join(f'<li>{rec}</li>' for rec in issue.recommendations)}
                    </ul>
                </div>
                {self._generate_fix_examples_html(issue)}
            </div>
            """
        
        return f"""
        <section class="critical-issues-section">
            <h2>🚨 Critical Issues ({len(critical_issues)})</h2>
            <div class="critical-issues-container">
                {issues_html}
            </div>
        </section>
        """

    def _generate_fix_examples_html(self, result: TestResult) -> str:
        """Generate fix examples HTML for a test result"""
        if not result.fix_examples:
            return ""

        examples_html = ""
        for example in result.fix_examples:
            examples_html += f"""
            <div class="fix-example">
                <code>{example}</code>
            </div>
            """

        return f"""
        <div class="fix-examples">
            <h5>💡 Fix Examples:</h5>
            {examples_html}
        </div>
        """

    def _generate_detailed_results_section(self, test_suite: TestSuite) -> str:
        """Generate detailed results section"""
        categories = {}
        for result in test_suite.results:
            category = result.category or 'general'
            if category not in categories:
                categories[category] = []
            categories[category].append(result)

        sections_html = ""
        for category, results in categories.items():
            sections_html += f"""
            <div class="category-section">
                <h3>{category.replace('_', ' ').title()} Tests ({len(results)})</h3>
                <div class="results-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Test Name</th>
                                <th>Status</th>
                                <th>Severity</th>
                                <th>Details</th>
                                <th>Execution Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            {''.join(self._generate_result_row_html(result) for result in results)}
                        </tbody>
                    </table>
                </div>
            </div>
            """

        return f"""
        <section class="detailed-results-section">
            <h2>📋 Detailed Test Results</h2>
            {sections_html}
        </section>
        """

    def _generate_result_row_html(self, result: TestResult) -> str:
        """Generate HTML row for a test result"""
        status_class = result.status.lower()
        severity_class = result.severity.lower()

        return f"""
        <tr class="result-row {status_class}">
            <td class="test-name">{result.test_name.replace('_', ' ').title()}</td>
            <td class="status">
                <span class="status-badge {status_class}">{result.status}</span>
            </td>
            <td class="severity">
                <span class="severity-badge {severity_class}">{result.severity}</span>
            </td>
            <td class="details">{result.details}</td>
            <td class="execution-time">{result.execution_time:.2f}s</td>
        </tr>
        """

    def _generate_fix_recommendations_section(self, test_suite: TestSuite) -> str:
        """Generate fix recommendations section"""
        all_recommendations = self._extract_all_recommendations(test_suite)

        if not all_recommendations:
            return ""

        # Group recommendations by priority
        critical_recs = [rec for rec in all_recommendations if 'critical' in rec.lower() or 'fix' in rec.lower()]
        other_recs = [rec for rec in all_recommendations if rec not in critical_recs]

        recommendations_html = ""

        if critical_recs:
            recommendations_html += """
            <div class="recommendations-group critical">
                <h3>🚨 Critical Fixes Needed</h3>
                <ul>
            """
            for rec in critical_recs[:10]:  # Limit to top 10
                recommendations_html += f"<li>{rec}</li>"
            recommendations_html += "</ul></div>"

        if other_recs:
            recommendations_html += """
            <div class="recommendations-group general">
                <h3>💡 General Improvements</h3>
                <ul>
            """
            for rec in other_recs[:15]:  # Limit to top 15
                recommendations_html += f"<li>{rec}</li>"
            recommendations_html += "</ul></div>"

        return f"""
        <section class="fix-recommendations-section">
            <h2>🔧 Fix Recommendations</h2>
            {recommendations_html}
        </section>
        """

    def _generate_charts_section(self, test_suite: TestSuite) -> str:
        """Generate charts section with visual data"""
        return """
        <section class="charts-section">
            <h2>📈 Visual Analysis</h2>
            <div class="charts-grid">
                <div class="chart-container">
                    <h3>Test Results Distribution</h3>
                    <canvas id="resultsChart" width="400" height="200"></canvas>
                </div>
                <div class="chart-container">
                    <h3>Severity Breakdown</h3>
                    <canvas id="severityChart" width="400" height="200"></canvas>
                </div>
                <div class="chart-container">
                    <h3>Category Performance</h3>
                    <canvas id="categoryChart" width="400" height="200"></canvas>
                </div>
            </div>
        </section>
        """

    def _extract_all_recommendations(self, test_suite: TestSuite) -> List[str]:
        """Extract all unique recommendations"""
        recommendations = set()
        for result in test_suite.results:
            recommendations.update(result.recommendations)
        return list(recommendations)

    def _extract_all_fix_examples(self, test_suite: TestSuite) -> List[str]:
        """Extract all fix examples"""
        fix_examples = set()
        for result in test_suite.results:
            fix_examples.update(result.fix_examples)
        return list(fix_examples)

    def _extract_code_locations(self, test_suite: TestSuite) -> List[str]:
        """Extract all code locations"""
        locations = set()
        for result in test_suite.results:
            if result.code_location:
                locations.add(result.code_location)
        return list(locations)

    def _generate_statistics(self, test_suite: TestSuite) -> Dict[str, Any]:
        """Generate detailed statistics"""
        results = test_suite.results

        # Category breakdown
        categories = {}
        for result in results:
            category = result.category or 'general'
            if category not in categories:
                categories[category] = {'total': 0, 'passed': 0, 'failed': 0}
            categories[category]['total'] += 1
            if result.is_failure():
                categories[category]['failed'] += 1
            else:
                categories[category]['passed'] += 1

        # Severity breakdown
        severities = {}
        for result in results:
            severity = result.severity
            severities[severity] = severities.get(severity, 0) + 1

        return {
            'categories': categories,
            'severities': severities,
            'avg_execution_time': sum(r.execution_time for r in results) / len(results) if results else 0,
            'slowest_test': max(results, key=lambda r: r.execution_time).test_name if results else None,
            'fastest_test': min(results, key=lambda r: r.execution_time).test_name if results else None
        }

    def _get_css_styles(self) -> str:
        """Get CSS styles for HTML report"""
        return """
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .report-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .report-header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .report-meta p { margin: 5px 0; opacity: 0.9; }
        .summary-section { background: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .summary-card.success { border-left-color: #28a745; }
        .summary-card.warning { border-left-color: #ffc107; }
        .summary-card.critical { border-left-color: #dc3545; }
        .status-indicator { font-size: 1.2em; font-weight: bold; margin-top: 10px; }
        .status-indicator.success { color: #28a745; }
        .status-indicator.warning { color: #ffc107; }
        .status-indicator.critical { color: #dc3545; }
        .test-stats { display: flex; justify-content: space-between; margin-top: 10px; }
        .stat { text-align: center; }
        .stat-number { display: block; font-size: 2em; font-weight: bold; }
        .stat-number.passed { color: #28a745; }
        .stat-number.failed { color: #dc3545; }
        .stat-number.critical { color: #dc3545; font-weight: 900; }
        .stat-label { font-size: 0.9em; color: #666; }
        .progress-bar { background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { background: linear-gradient(90deg, #28a745, #20c997); height: 100%; transition: width 0.3s ease; }
        .percentage { font-size: 1.2em; font-weight: bold; color: #28a745; }
        .execution-time .time-value { font-size: 1.5em; font-weight: bold; color: #007bff; }
        .critical-issues-section { background: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .critical-issue { background: #fff5f5; border: 1px solid #fed7d7; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
        .issue-header { display: flex; justify-content: between; align-items: center; margin-bottom: 15px; }
        .severity-badge { padding: 4px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold; text-transform: uppercase; }
        .severity-badge.critical { background: #dc3545; color: white; }
        .severity-badge.high { background: #fd7e14; color: white; }
        .severity-badge.medium { background: #ffc107; color: #212529; }
        .severity-badge.low { background: #6c757d; color: white; }
        .issue-details { margin-bottom: 15px; }
        .issue-recommendations ul { margin-left: 20px; }
        .fix-examples { margin-top: 15px; }
        .fix-example { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .fix-example code { font-family: 'Monaco', 'Menlo', monospace; }
        .charts-section { background: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .charts-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-top: 20px; }
        .chart-container { text-align: center; }
        .detailed-results-section { background: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .category-section { margin-bottom: 30px; }
        .results-table table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .results-table th, .results-table td { padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; }
        .results-table th { background: #f8f9fa; font-weight: 600; }
        .result-row.failed { background: #fff5f5; }
        .result-row.passed { background: #f0fff4; }
        .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }
        .status-badge.passed { background: #d4edda; color: #155724; }
        .status-badge.failed { background: #f8d7da; color: #721c24; }
        .status-badge.critical { background: #dc3545; color: white; }
        .report-footer { text-align: center; margin-top: 50px; padding: 20px; color: #666; border-top: 1px solid #dee2e6; }
        """

    def _get_javascript(self) -> str:
        """Get JavaScript for interactive charts"""
        return """
        // Chart.js configuration would go here
        console.log('testerat Enhanced Report loaded');
        """

    def _generate_pdf_report(self, test_suite: TestSuite, framework_info: Dict[str, Any] = None, app_info: Dict[str, Any] = None) -> str:
        """Generate PDF report (placeholder)"""
        # PDF generation would require additional libraries like reportlab
        self.logger.info("PDF generation not implemented yet")
        return ""
