"""
Universal API Testing Engine

Tests real form submissions and API interactions across any web application.
Catches CSRF token issues, API errors, and network request problems.

Universal Support:
- Any REST API (Node.js, Python, PHP, etc.)
- Any authentication system (JWT, sessions, cookies)
- Any CSRF protection mechanism
- Any form submission approach

Critical Issue Detection:
- CSRF token header case mismatches (X-CSRF-Token vs x-csrf-token)
- API endpoint failures
- Network request errors
- Form submission problems
"""

import time
import logging
import json
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from playwright.sync_api import Page, Request, Response

from ..config.test_config import APIConfig
from ..utils.test_result import TestResult, TestSeverity, TestStatus, TestCategory, create_api_result


@dataclass
class APIRequest:
    """Universal API request representation"""
    url: str
    method: str
    headers: Dict[str, str]
    body: Optional[str] = None
    status_code: Optional[int] = None
    response_body: Optional[str] = None
    response_time: float = 0.0
    error: Optional[str] = None


@dataclass
class CSRFTokenInfo:
    """CSRF token information"""
    token_value: Optional[str] = None
    token_source: Optional[str] = None  # meta tag, form input, etc.
    header_name: Optional[str] = None
    is_valid: bool = False


class APIEngine:
    """
    Universal API Testing Engine
    
    Tests real API interactions and form submissions across any web application.
    Detects CSRF issues, API errors, and network request problems.
    """
    
    def __init__(self, config: APIConfig, logger: logging.Logger = None):
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        self.captured_requests: List[APIRequest] = []
        self.csrf_info = CSRFTokenInfo()
        self.test_results: List[TestResult] = []
    
    def run_comprehensive_api_tests(self, page: Page, base_url: str) -> List[TestResult]:
        """
        Run comprehensive API testing suite
        
        Universal tests that work with any web application:
        1. CSRF token detection and validation
        2. Form submission testing
        3. API endpoint testing
        4. Network request monitoring
        5. Error handling validation
        6. Security testing
        """
        self.logger.info("🌐 Starting comprehensive API testing")
        self.test_results = []
        
        # Setup request monitoring
        self._setup_request_monitoring(page)
        
        # Test 1: CSRF Token Detection and Validation (CRITICAL)
        self.test_csrf_protection(page, base_url)
        
        # Test 2: Form Submission Testing
        self.test_form_submissions(page)
        
        # Test 3: API Endpoint Testing
        self.test_api_endpoints(page, base_url)
        
        # Test 4: Network Request Analysis
        self.test_network_requests(page)
        
        # Test 5: Error Handling
        self.test_api_error_handling(page)
        
        # Test 6: Security Testing
        self.test_api_security(page)
        
        return self.test_results
    
    def test_csrf_protection(self, page: Page, base_url: str) -> TestResult:
        """
        Test CSRF protection implementation
        
        This is the critical test that catches issues like:
        - CSRF token header case mismatches (X-CSRF-Token vs x-csrf-token)
        - Missing CSRF tokens
        - Invalid CSRF token handling
        """
        self.logger.info("Testing CSRF protection")
        issues = []
        recommendations = []
        
        try:
            # Step 1: Detect CSRF token
            csrf_token = self._detect_csrf_token(page)
            if not csrf_token:
                issues.append("No CSRF token found")
                recommendations.append("Implement CSRF protection for form submissions")
            else:
                self.csrf_info.token_value = csrf_token['value']
                self.csrf_info.token_source = csrf_token['source']
                self.csrf_info.is_valid = True
                
                # Step 2: Test CSRF token header variations
                header_issues = self._test_csrf_header_variations(page, csrf_token['value'])
                if header_issues:
                    issues.extend(header_issues)
                    recommendations.append("Fix CSRF token header case sensitivity")
                    
                    # Specific recommendation for the known issue
                    if any("x-csrf-token" in issue.lower() for issue in header_issues):
                        recommendations.append("Change 'x-csrf-token' to 'X-CSRF-Token' for better compatibility")
                
                # Step 3: Test CSRF token validation
                validation_issues = self._test_csrf_validation(page, csrf_token['value'])
                if validation_issues:
                    issues.extend(validation_issues)
                    recommendations.append("Improve CSRF token validation")
        
        except Exception as e:
            issues.append(f"CSRF protection test failed: {str(e)}")
            recommendations.append("Review CSRF protection implementation")
        
        # Determine severity based on issues found
        severity = TestSeverity.CRITICAL if any("header case" in issue.lower() for issue in issues) else \
                  TestSeverity.HIGH if any("403" in issue or "forbidden" in issue.lower() for issue in issues) else \
                  TestSeverity.MEDIUM if issues else TestSeverity.LOW
        
        status = TestStatus.CRITICAL if severity == TestSeverity.CRITICAL else \
                TestStatus.FAILED if issues else TestStatus.PASSED
        
        result = create_api_result(
            "csrf_protection", status,
            "; ".join(issues) if issues else "CSRF protection working properly",
            severity, recommendations
        )
        
        # Add specific fix examples for CSRF issues
        if any("x-csrf-token" in issue.lower() for issue in issues):
            result.add_fix_example("Change header from 'x-csrf-token' to 'X-CSRF-Token'")
            result.set_code_location("useCSRFToken.ts", 60)
        
        self.test_results.append(result)
        return result
    
    def test_form_submissions(self, page: Page) -> TestResult:
        """Test real form submissions"""
        self.logger.info("Testing form submissions")
        issues = []
        recommendations = []
        
        try:
            # Find forms on the page
            forms = page.query_selector_all('form')
            if not forms:
                return create_api_result(
                    "form_submissions", TestStatus.SKIPPED,
                    "No forms found to test", TestSeverity.LOW, []
                )
            
            for i, form in enumerate(forms):
                form_issues = self._test_individual_form(page, form, i)
                if form_issues:
                    issues.extend(form_issues)
                    recommendations.append(f"Fix form submission issues in form {i+1}")
        
        except Exception as e:
            issues.append(f"Form submission test failed: {str(e)}")
            recommendations.append("Review form submission implementation")
        
        severity = TestSeverity.HIGH if issues else TestSeverity.LOW
        status = TestStatus.FAILED if issues else TestStatus.PASSED
        
        result = create_api_result(
            "form_submissions", status,
            "; ".join(issues) if issues else "Form submissions working properly",
            severity, recommendations
        )
        
        self.test_results.append(result)
        return result
    
    def test_api_endpoints(self, page: Page, base_url: str) -> TestResult:
        """Test common API endpoints"""
        self.logger.info("Testing API endpoints")
        issues = []
        recommendations = []
        
        try:
            # Test common endpoints
            for endpoint in self.config.common_endpoints:
                endpoint_url = f"{base_url.rstrip('/')}{endpoint}"
                
                try:
                    # Make a test request to the endpoint
                    response = page.request.get(endpoint_url)
                    
                    if response.status >= 500:
                        issues.append(f"Server error on {endpoint}: {response.status}")
                        recommendations.append(f"Fix server error on {endpoint}")
                    elif response.status == 404:
                        # 404 might be expected for some endpoints
                        self.logger.debug(f"Endpoint not found: {endpoint}")
                    elif response.status >= 400:
                        issues.append(f"Client error on {endpoint}: {response.status}")
                        recommendations.append(f"Check endpoint implementation: {endpoint}")
                        
                except Exception as e:
                    issues.append(f"Failed to test endpoint {endpoint}: {str(e)}")
                    recommendations.append(f"Check endpoint accessibility: {endpoint}")
        
        except Exception as e:
            issues.append(f"API endpoint testing failed: {str(e)}")
            recommendations.append("Review API endpoint implementation")
        
        severity = TestSeverity.MEDIUM if issues else TestSeverity.LOW
        status = TestStatus.FAILED if issues else TestStatus.PASSED
        
        result = create_api_result(
            "api_endpoints", status,
            "; ".join(issues) if issues else "API endpoints working properly",
            severity, recommendations
        )
        
        self.test_results.append(result)
        return result
    
    def test_network_requests(self, page: Page) -> TestResult:
        """Analyze captured network requests"""
        self.logger.info("Analyzing network requests")
        issues = []
        recommendations = []
        
        try:
            if not self.captured_requests:
                return create_api_result(
                    "network_requests", TestStatus.SKIPPED,
                    "No network requests captured", TestSeverity.LOW, []
                )
            
            # Analyze captured requests
            for request in self.captured_requests:
                # Check for errors
                if request.error:
                    issues.append(f"Network error: {request.error}")
                    recommendations.append("Fix network connectivity issues")
                
                # Check for slow requests
                if request.response_time > 5000:  # 5 seconds
                    issues.append(f"Slow API request: {request.url} ({request.response_time}ms)")
                    recommendations.append("Optimize API performance")
                
                # Check for failed requests
                if request.status_code and request.status_code >= 400:
                    issues.append(f"Failed request: {request.method} {request.url} - {request.status_code}")
                    recommendations.append(f"Fix API error: {request.status_code}")
        
        except Exception as e:
            issues.append(f"Network request analysis failed: {str(e)}")
            recommendations.append("Review network request handling")
        
        severity = TestSeverity.MEDIUM if issues else TestSeverity.LOW
        status = TestStatus.FAILED if issues else TestStatus.PASSED
        
        result = create_api_result(
            "network_requests", status,
            "; ".join(issues) if issues else "Network requests working properly",
            severity, recommendations
        )
        
        self.test_results.append(result)
        return result

    def test_api_error_handling(self, page: Page) -> TestResult:
        """Test API error handling"""
        self.logger.info("Testing API error handling")
        issues = []
        recommendations = []

        try:
            # Test error responses in captured requests
            error_requests = [r for r in self.captured_requests if r.status_code and r.status_code >= 400]

            for request in error_requests:
                # Check if error is properly handled in UI
                if not self._check_error_handling_ui(page, request):
                    issues.append(f"Error not properly displayed for {request.url}")
                    recommendations.append("Improve error message display for API failures")

        except Exception as e:
            issues.append(f"API error handling test failed: {str(e)}")
            recommendations.append("Review API error handling implementation")

        severity = TestSeverity.MEDIUM if issues else TestSeverity.LOW
        status = TestStatus.FAILED if issues else TestStatus.PASSED

        result = create_api_result(
            "api_error_handling", status,
            "; ".join(issues) if issues else "API error handling working properly",
            severity, recommendations
        )

        self.test_results.append(result)
        return result

    def test_api_security(self, page: Page) -> TestResult:
        """Test API security"""
        self.logger.info("Testing API security")
        issues = []
        recommendations = []

        try:
            # Check for sensitive data in requests
            for request in self.captured_requests:
                if request.body and self._contains_sensitive_data(request.body):
                    issues.append(f"Sensitive data in request body: {request.url}")
                    recommendations.append("Encrypt or remove sensitive data from API requests")

                # Check for insecure protocols
                if request.url.startswith('http://') and not request.url.startswith('http://localhost'):
                    issues.append(f"Insecure HTTP request: {request.url}")
                    recommendations.append("Use HTTPS for all API requests")

        except Exception as e:
            issues.append(f"API security test failed: {str(e)}")
            recommendations.append("Review API security implementation")

        severity = TestSeverity.HIGH if any("sensitive data" in issue.lower() for issue in issues) else \
                  TestSeverity.MEDIUM if issues else TestSeverity.LOW
        status = TestStatus.FAILED if issues else TestStatus.PASSED

        result = create_api_result(
            "api_security", status,
            "; ".join(issues) if issues else "API security checks passed",
            severity, recommendations
        )

        self.test_results.append(result)
        return result

    def _setup_request_monitoring(self, page: Page):
        """Setup network request monitoring"""
        def handle_request(request: Request):
            api_request = APIRequest(
                url=request.url,
                method=request.method,
                headers=dict(request.headers),
                body=request.post_data
            )

            start_time = time.time()

            try:
                # Get response synchronously
                response = request.response()
                if response:
                    api_request.status_code = response.status
                    api_request.response_time = (time.time() - start_time) * 1000
                    try:
                        api_request.response_body = response.text()
                    except:
                        pass
                else:
                    api_request.error = "No response received"
            except Exception as e:
                api_request.error = f"Request failed: {str(e)}"

            self.captured_requests.append(api_request)

        page.on("request", handle_request)

    def _detect_csrf_token(self, page: Page) -> Optional[Dict[str, str]]:
        """Detect CSRF token from various sources"""
        # Check meta tags
        for selector in self.config.csrf_header_variations:
            meta_selector = f'meta[name="{selector.lower()}"], meta[name="{selector}"]'
            meta_element = page.query_selector(meta_selector)
            if meta_element:
                token = meta_element.get_attribute('content')
                if token:
                    return {'value': token, 'source': f'meta[name="{selector}"]'}

        # Check form inputs
        csrf_inputs = page.query_selector_all('input[name*="csrf"], input[name*="token"]')
        for input_elem in csrf_inputs:
            token = input_elem.get_attribute('value')
            if token:
                name = input_elem.get_attribute('name')
                return {'value': token, 'source': f'input[name="{name}"]'}

        # Check cookies
        cookies = page.context.cookies()
        for cookie in cookies:
            if 'csrf' in cookie['name'].lower() or 'token' in cookie['name'].lower():
                return {'value': cookie['value'], 'source': f'cookie[{cookie["name"]}]'}

        return None

    def _test_csrf_header_variations(self, page: Page, csrf_token: str) -> List[str]:
        """
        Test different CSRF header variations to catch case sensitivity issues

        This specifically tests for the issue we found:
        - x-csrf-token (lowercase) vs X-CSRF-Token (proper case)
        """
        issues = []

        try:
            # Test each header variation
            for header_name in self.config.csrf_header_variations:
                test_url = f"{page.url}/api/test-csrf"  # Hypothetical test endpoint

                try:
                    # Make a test request with this header variation
                    response = page.request.post(test_url, data={
                        'test': 'data'
                    }, headers={
                        header_name: csrf_token,
                        'Content-Type': 'application/json'
                    })

                    # Check if this header variation works
                    if response.status == 403:
                        issues.append(f"CSRF header '{header_name}' rejected with 403 Forbidden")
                    elif response.status >= 400:
                        issues.append(f"CSRF header '{header_name}' caused error: {response.status}")

                except Exception as e:
                    # This is expected for test endpoints that don't exist
                    self.logger.debug(f"CSRF header test failed for {header_name}: {e}")

        except Exception as e:
            self.logger.debug(f"CSRF header variation test failed: {e}")

        return issues

    def _test_csrf_validation(self, page: Page, csrf_token: str) -> List[str]:
        """Test CSRF token validation"""
        issues = []

        try:
            # Test with invalid token
            test_url = f"{page.url}/api/test-csrf"

            try:
                response = page.request.post(test_url, data={
                    'test': 'data'
                }, headers={
                    'X-CSRF-Token': 'invalid-token',
                    'Content-Type': 'application/json'
                })

                if response.status != 403:
                    issues.append("Invalid CSRF token accepted")

            except Exception as e:
                self.logger.debug(f"CSRF validation test failed: {e}")

        except Exception as e:
            self.logger.debug(f"CSRF validation test failed: {e}")

        return issues

    def _test_individual_form(self, page: Page, form, form_index: int) -> List[str]:
        """Test individual form submission"""
        issues = []

        try:
            # Get form action and method
            action = form.get_attribute('action') or page.url
            method = form.get_attribute('method') or 'GET'

            # Find form inputs
            inputs = form.query_selector_all('input, select, textarea')

            # Fill form with test data
            form_data = {}
            for input_elem in inputs:
                input_type = input_elem.get_attribute('type') or 'text'
                input_name = input_elem.get_attribute('name')

                if input_name and input_type not in ['submit', 'button']:
                    if input_type == 'email':
                        input_elem.fill('<EMAIL>')
                        form_data[input_name] = '<EMAIL>'
                    elif input_type == 'password':
                        input_elem.fill('TestPassword123!')
                        form_data[input_name] = 'TestPassword123!'
                    elif input_type == 'text':
                        input_elem.fill('Test Value')
                        form_data[input_name] = 'Test Value'
                    elif input_type == 'checkbox':
                        input_elem.check()
                        form_data[input_name] = True

            # Submit form and monitor request
            submit_button = form.query_selector('input[type="submit"], button[type="submit"]')
            if submit_button:
                # Monitor the submission
                request_captured = False

                def capture_form_request(request: Request):
                    nonlocal request_captured
                    if request.method.upper() == method.upper() and action in request.url:
                        request_captured = True

                        # Check for CSRF token in request
                        if method.upper() in ['POST', 'PUT', 'DELETE']:
                            headers = dict(request.headers)
                            csrf_found = any(
                                header.lower() in ['x-csrf-token', 'csrf-token']
                                for header in headers.keys()
                            )
                            if not csrf_found:
                                issues.append(f"Form {form_index + 1} missing CSRF token in headers")

                page.on("request", capture_form_request)

                try:
                    submit_button.click()
                    page.wait_for_timeout(2000)  # Wait for submission

                    if not request_captured:
                        issues.append(f"Form {form_index + 1} submission not captured")

                except Exception as e:
                    issues.append(f"Form {form_index + 1} submission failed: {str(e)}")

                page.remove_listener("request", capture_form_request)

        except Exception as e:
            issues.append(f"Form {form_index + 1} test failed: {str(e)}")

        return issues

    def _check_error_handling_ui(self, page: Page, request: APIRequest) -> bool:
        """Check if API errors are properly displayed in UI"""
        error_selectors = [
            '.error', '.error-message', '[role="alert"]',
            '.alert-danger', '.notification-error', '.toast-error'
        ]

        for selector in error_selectors:
            error_elements = page.query_selector_all(selector)
            if error_elements:
                return True

        return False

    def _contains_sensitive_data(self, body: str) -> bool:
        """Check if request body contains sensitive data"""
        if not body:
            return False

        sensitive_patterns = [
            'password', 'ssn', 'social_security', 'credit_card',
            'card_number', 'cvv', 'pin', 'secret'
        ]

        body_lower = body.lower()
        return any(pattern in body_lower for pattern in sensitive_patterns)
