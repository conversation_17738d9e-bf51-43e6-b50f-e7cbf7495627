"""
Universal Authentication Testing Engine

Tests authenticated user experiences across any web application framework.
Catches authentication state issues, session management problems, and access control bugs.

Universal Support:
- Any authentication system (NextAuth, Auth0, custom forms, OAuth)
- Any web framework (React, Vue, Angular, vanilla JS)
- Any session management approach (cookies, JWT, localStorage)
"""

import time
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from playwright.sync_api import Page, expect

from ..config.test_config import AuthConfig, AuthType
from ..utils.test_result import TestResult, TestSeverity


@dataclass
class AuthenticationState:
    """Universal authentication state representation"""
    is_authenticated: bool = False
    user_identifier: Optional[str] = None  # email, username, or ID
    session_data: Dict[str, Any] = None
    auth_method: Optional[AuthType] = None
    session_expires: Optional[float] = None
    csrf_token: Optional[str] = None


class AuthenticationEngine:
    """
    Universal Authentication Testing Engine
    
    Tests authentication flows and authenticated user experiences
    across any web application framework and authentication system.
    """
    
    def __init__(self, config: AuthConfig, logger: logging.Logger = None):
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        self.current_auth_state = AuthenticationState()
        self.test_results: List[TestResult] = []
    
    def run_comprehensive_auth_tests(self, page: Page, base_url: str) -> List[TestResult]:
        """
        Run comprehensive authentication testing suite
        
        Universal tests that work with any authentication system:
        1. Authentication flow testing
        2. Session management validation
        3. Protected route access testing
        4. Authentication state consistency
        5. Logout and session cleanup
        """
        self.logger.info("🔐 Starting comprehensive authentication testing")
        self.test_results = []
        
        # Test 1: Authentication Flow
        self.test_authentication_flow(page, base_url)
        
        # Test 2: Authentication State Consistency
        self.test_auth_state_consistency(page)
        
        # Test 3: Protected Route Access
        self.test_protected_route_access(page, base_url)
        
        # Test 4: Session Management
        self.test_session_management(page)
        
        # Test 5: Logout Flow
        self.test_logout_flow(page)
        
        # Test 6: Authentication Edge Cases
        self.test_auth_edge_cases(page, base_url)
        
        return self.test_results
    
    def test_authentication_flow(self, page: Page, base_url: str) -> TestResult:
        """Test complete authentication flow - universal approach"""
        self.logger.info("Testing authentication flow")
        issues = []
        recommendations = []
        
        try:
            # Navigate to login page
            login_url = f"{base_url.rstrip('/')}{self.config.login_url}"
            page.goto(login_url, wait_until='networkidle')
            
            # Detect authentication method
            auth_method = self._detect_auth_method(page)
            self.current_auth_state.auth_method = auth_method
            
            if auth_method == AuthType.FORM_BASED:
                result = self._test_form_based_auth(page, issues, recommendations)
            elif auth_method == AuthType.OAUTH:
                result = self._test_oauth_auth(page, issues, recommendations)
            else:
                result = self._test_generic_auth(page, issues, recommendations)
            
            # Verify authentication success
            if self._verify_authentication_success(page):
                self.current_auth_state.is_authenticated = True
                self.current_auth_state.user_identifier = self._extract_user_identifier(page)
            else:
                issues.append("Authentication flow completed but user not properly authenticated")
                recommendations.append("Verify authentication success indicators and user state management")
            
        except Exception as e:
            issues.append(f"Authentication flow failed: {str(e)}")
            recommendations.append("Check login form accessibility and authentication endpoint")
        
        severity = TestSeverity.CRITICAL if issues else TestSeverity.LOW
        status = "FAILED" if issues else "PASSED"

        result = TestResult(
            test_name="authentication_flow",
            status=status,
            details="; ".join(issues) if issues else "Authentication flow successful",
            severity=severity.value,
            recommendations=recommendations,
            execution_time=0.0
        )
        
        self.test_results.append(result)
        return result
    
    def test_auth_state_consistency(self, page: Page) -> TestResult:
        """
        Test authentication state consistency - catches useSession loading issues
        
        This is the critical test that catches issues like:
        - useSession loading state not handled properly
        - Authentication state flickering
        - Logged-out content shown to authenticated users
        """
        self.logger.info("Testing authentication state consistency")
        issues = []
        recommendations = []
        
        if not self.current_auth_state.is_authenticated:
            return TestResult(
                "auth_state_consistency", "SKIPPED",
                "Skipped - user not authenticated", TestSeverity.LOW.value, [], 0.0
            )
        
        try:
            # Test 1: Check for loading states that never resolve
            loading_indicators = self._find_loading_indicators(page)
            if loading_indicators:
                # Wait and check if loading states resolve
                time.sleep(2)
                persistent_loading = self._find_loading_indicators(page)
                if persistent_loading:
                    issues.append("Authentication loading state never resolves")
                    recommendations.append("Fix useSession or auth state loading handling")
            
            # Test 2: Check for logged-out content shown to authenticated users
            logged_out_content = self._find_logged_out_content(page)
            if logged_out_content:
                issues.append("Logged-out content visible to authenticated user")
                recommendations.append("Ensure authentication state is properly checked before rendering content")
            
            # Test 3: Check for authentication state flickering
            auth_indicators_before = self._get_auth_indicators(page)
            page.wait_for_timeout(1000)  # Wait 1 second
            auth_indicators_after = self._get_auth_indicators(page)
            
            if auth_indicators_before != auth_indicators_after:
                issues.append("Authentication state flickering detected")
                recommendations.append("Implement proper authentication state management to prevent flickering")
            
            # Test 4: Verify user-specific content is displayed
            user_content = self._find_user_specific_content(page)
            if not user_content:
                issues.append("No user-specific content found for authenticated user")
                recommendations.append("Ensure authenticated users see personalized content")
                
        except Exception as e:
            issues.append(f"Auth state consistency test failed: {str(e)}")
            recommendations.append("Review authentication state management implementation")
        
        severity = TestSeverity.CRITICAL if any("loading state never resolves" in issue for issue in issues) else \
                  TestSeverity.HIGH if issues else TestSeverity.LOW
        status = "FAILED" if issues else "PASSED"

        result = TestResult(
            test_name="auth_state_consistency",
            status=status,
            details="; ".join(issues) if issues else "Authentication state consistent",
            severity=severity.value,
            recommendations=recommendations,
            execution_time=0.0
        )
        
        self.test_results.append(result)
        return result
    
    def test_protected_route_access(self, page: Page, base_url: str) -> TestResult:
        """Test access to protected routes"""
        self.logger.info("Testing protected route access")
        issues = []
        recommendations = []
        
        for route in self.config.protected_routes:
            try:
                protected_url = f"{base_url.rstrip('/')}{route}"
                page.goto(protected_url, wait_until='networkidle')
                
                if self.current_auth_state.is_authenticated:
                    # Should have access
                    if self._is_redirected_to_login(page):
                        issues.append(f"Authenticated user redirected from protected route: {route}")
                        recommendations.append(f"Fix access control for {route}")
                else:
                    # Should be redirected or blocked
                    if not self._is_redirected_to_login(page) and not self._is_access_denied(page):
                        issues.append(f"Unauthenticated access allowed to protected route: {route}")
                        recommendations.append(f"Implement proper access control for {route}")
                        
            except Exception as e:
                issues.append(f"Error testing protected route {route}: {str(e)}")
                recommendations.append(f"Check route accessibility: {route}")
        
        severity = TestSeverity.HIGH if issues else TestSeverity.LOW
        status = "FAILED" if issues else "PASSED"

        result = TestResult(
            test_name="protected_route_access",
            status=status,
            details="; ".join(issues) if issues else "Protected routes properly secured",
            severity=severity.value,
            recommendations=recommendations,
            execution_time=0.0
        )
        
        self.test_results.append(result)
        return result
    
    def _detect_auth_method(self, page: Page) -> AuthType:
        """Universal authentication method detection"""
        # Check for OAuth buttons
        oauth_indicators = page.query_selector_all(
            'button:has-text("Google"), button:has-text("GitHub"), button:has-text("OAuth"), .oauth-btn'
        )
        if oauth_indicators:
            return AuthType.OAUTH
        
        # Check for form-based auth
        email_input = page.query_selector(self.config.selectors["email_input"])
        password_input = page.query_selector(self.config.selectors["password_input"])
        if email_input and password_input:
            return AuthType.FORM_BASED
        
        # Check for NextAuth indicators
        if page.query_selector('[data-provider]') or 'next-auth' in page.url:
            return AuthType.NEXTAUTH
        
        return AuthType.CUSTOM
    
    def _test_form_based_auth(self, page: Page, issues: List[str], recommendations: List[str]) -> bool:
        """Test form-based authentication"""
        try:
            # Fill login form
            email_input = page.query_selector(self.config.selectors["email_input"])
            password_input = page.query_selector(self.config.selectors["password_input"])
            login_button = page.query_selector(self.config.selectors["login_button"])
            
            if not email_input:
                issues.append("Email input field not found")
                return False
            if not password_input:
                issues.append("Password input field not found")
                return False
            if not login_button:
                issues.append("Login button not found")
                return False
            
            # Use test credentials
            test_user = self.config.test_users["standard"]
            email_input.fill(test_user["email"])
            password_input.fill(test_user["password"])
            
            # Submit form
            login_button.click()
            page.wait_for_load_state('networkidle')
            
            return True
            
        except Exception as e:
            issues.append(f"Form-based authentication failed: {str(e)}")
            return False
    
    def _verify_authentication_success(self, page: Page) -> bool:
        """Universal authentication success verification"""
        # Check for authentication indicators
        auth_indicator = page.query_selector(self.config.selectors["auth_indicator"])
        if auth_indicator:
            return True
        
        # Check if redirected away from login
        if self.config.login_url not in page.url:
            return True
        
        # Check for user menu
        user_menu = page.query_selector(self.config.selectors["user_menu"])
        if user_menu:
            return True
        
        return False
    
    def _find_loading_indicators(self, page: Page) -> List[str]:
        """Find authentication loading indicators"""
        loading_selectors = [
            '.loading', '.spinner', '[data-testid="loading"]',
            'text="Loading..."', 'text="Authenticating..."',
            '.skeleton', '.auth-loading'
        ]
        
        found_loading = []
        for selector in loading_selectors:
            elements = page.query_selector_all(selector)
            if elements:
                found_loading.append(selector)
        
        return found_loading
    
    def _find_logged_out_content(self, page: Page) -> List[str]:
        """Find content that should only be visible to logged-out users"""
        logged_out_selectors = [
            'text="Sign In"', 'text="Log In"', 'text="Login"',
            '.login-prompt', '.auth-required', 'text="Please log in"'
        ]
        
        found_content = []
        for selector in logged_out_selectors:
            elements = page.query_selector_all(selector)
            if elements:
                found_content.append(selector)
        
        return found_content

    def _get_auth_indicators(self, page: Page) -> Dict[str, bool]:
        """Get current authentication indicators for state comparison"""
        return {
            "auth_indicator": bool(page.query_selector(self.config.selectors["auth_indicator"])),
            "user_menu": bool(page.query_selector(self.config.selectors["user_menu"])),
            "logout_button": bool(page.query_selector(self.config.selectors["logout_button"])),
            "login_form": bool(page.query_selector(self.config.selectors["email_input"]))
        }

    def _find_user_specific_content(self, page: Page) -> List[str]:
        """Find user-specific content that should be visible to authenticated users"""
        user_content_selectors = [
            '.user-name', '.username', '.profile-info',
            '[data-testid="user-info"]', '.dashboard', '.user-dashboard'
        ]

        found_content = []
        for selector in user_content_selectors:
            elements = page.query_selector_all(selector)
            if elements:
                found_content.append(selector)

        return found_content

    def _extract_user_identifier(self, page: Page) -> Optional[str]:
        """Extract user identifier from authenticated page"""
        # Try to find user email or name
        selectors = [
            '.user-email', '.user-name', '.username',
            '[data-testid="user-email"]', '[data-testid="user-name"]'
        ]

        for selector in selectors:
            element = page.query_selector(selector)
            if element:
                text = element.text_content()
                if text and text.strip():
                    return text.strip()

        return None

    def _is_redirected_to_login(self, page: Page) -> bool:
        """Check if page was redirected to login"""
        return self.config.login_url in page.url or 'login' in page.url.lower()

    def _is_access_denied(self, page: Page) -> bool:
        """Check if access was denied"""
        denied_indicators = [
            'text="Access Denied"', 'text="Unauthorized"', 'text="403"',
            '.access-denied', '.unauthorized', '.error-403'
        ]

        for selector in denied_indicators:
            if page.query_selector(selector):
                return True

        return False

    def test_session_management(self, page: Page) -> TestResult:
        """Test session management and persistence"""
        self.logger.info("Testing session management")
        issues = []
        recommendations = []

        if not self.current_auth_state.is_authenticated:
            return TestResult(
                "session_management", "SKIPPED",
                "Skipped - user not authenticated", TestSeverity.LOW.value, [], 0.0
            )

        try:
            # Test session persistence across page reloads
            original_url = page.url
            page.reload(wait_until='networkidle')

            if not self._verify_authentication_success(page):
                issues.append("Session not persisted across page reload")
                recommendations.append("Implement proper session persistence")

            # Test CSRF token if required
            if self.config.csrf_token_required:
                csrf_token = self._extract_csrf_token(page)
                if not csrf_token:
                    issues.append("CSRF token not found")
                    recommendations.append("Implement CSRF protection")
                else:
                    self.current_auth_state.csrf_token = csrf_token

        except Exception as e:
            issues.append(f"Session management test failed: {str(e)}")
            recommendations.append("Review session management implementation")

        severity = TestSeverity.HIGH if issues else TestSeverity.LOW
        status = "FAILED" if issues else "PASSED"

        result = TestResult(
            test_name="session_management",
            status=status,
            details="; ".join(issues) if issues else "Session management working properly",
            severity=severity.value,
            recommendations=recommendations,
            execution_time=0.0
        )

        self.test_results.append(result)
        return result

    def test_logout_flow(self, page: Page) -> TestResult:
        """Test logout functionality"""
        self.logger.info("Testing logout flow")
        issues = []
        recommendations = []

        if not self.current_auth_state.is_authenticated:
            return TestResult(
                "logout_flow", "SKIPPED",
                "Skipped - user not authenticated", TestSeverity.LOW.value, [], 0.0
            )

        try:
            # Find and click logout button
            logout_button = page.query_selector(self.config.selectors["logout_button"])
            if not logout_button:
                issues.append("Logout button not found")
                recommendations.append("Ensure logout functionality is accessible")
            else:
                logout_button.click()
                page.wait_for_load_state('networkidle')

                # Verify logout success
                if self._verify_authentication_success(page):
                    issues.append("User still appears authenticated after logout")
                    recommendations.append("Fix logout functionality and session cleanup")
                else:
                    self.current_auth_state.is_authenticated = False
                    self.current_auth_state.user_identifier = None

        except Exception as e:
            issues.append(f"Logout test failed: {str(e)}")
            recommendations.append("Review logout implementation")

        severity = TestSeverity.MEDIUM if issues else TestSeverity.LOW
        status = "FAILED" if issues else "PASSED"

        result = TestResult(
            test_name="logout_flow",
            status=status,
            details="; ".join(issues) if issues else "Logout flow working properly",
            severity=severity.value,
            recommendations=recommendations,
            execution_time=0.0
        )

        self.test_results.append(result)
        return result

    def test_auth_edge_cases(self, page: Page, base_url: str) -> TestResult:
        """Test authentication edge cases"""
        self.logger.info("Testing authentication edge cases")
        issues = []
        recommendations = []

        try:
            # Test invalid credentials
            login_url = f"{base_url.rstrip('/')}{self.config.login_url}"
            page.goto(login_url, wait_until='networkidle')

            # Try invalid login
            if self._test_invalid_credentials(page):
                issues.append("Invalid credentials accepted")
                recommendations.append("Implement proper credential validation")

            # Test empty form submission
            if self._test_empty_form_submission(page):
                issues.append("Empty form submission accepted")
                recommendations.append("Add form validation for required fields")

        except Exception as e:
            issues.append(f"Auth edge case testing failed: {str(e)}")
            recommendations.append("Review authentication edge case handling")

        severity = TestSeverity.MEDIUM if issues else TestSeverity.LOW
        status = "FAILED" if issues else "PASSED"

        result = TestResult(
            test_name="auth_edge_cases",
            status=status,
            details="; ".join(issues) if issues else "Authentication edge cases handled properly",
            severity=severity.value,
            recommendations=recommendations,
            execution_time=0.0
        )

        self.test_results.append(result)
        return result

    def _extract_csrf_token(self, page: Page) -> Optional[str]:
        """Extract CSRF token from page"""
        for selector in self.config.csrf_token_selectors:
            element = page.query_selector(selector)
            if element:
                token = element.get_attribute('content') or element.get_attribute('value')
                if token:
                    return token
        return None

    def _test_invalid_credentials(self, page: Page) -> bool:
        """Test with invalid credentials"""
        try:
            email_input = page.query_selector(self.config.selectors["email_input"])
            password_input = page.query_selector(self.config.selectors["password_input"])
            login_button = page.query_selector(self.config.selectors["login_button"])

            if email_input and password_input and login_button:
                email_input.fill("<EMAIL>")
                password_input.fill("wrongpassword")
                login_button.click()
                page.wait_for_load_state('networkidle')

                # Should not be authenticated
                return self._verify_authentication_success(page)
        except:
            pass
        return False

    def _test_empty_form_submission(self, page: Page) -> bool:
        """Test empty form submission"""
        try:
            email_input = page.query_selector(self.config.selectors["email_input"])
            password_input = page.query_selector(self.config.selectors["password_input"])
            login_button = page.query_selector(self.config.selectors["login_button"])

            if email_input and password_input and login_button:
                email_input.fill("")
                password_input.fill("")
                login_button.click()
                page.wait_for_load_state('networkidle')

                # Should not be authenticated
                return self._verify_authentication_success(page)
        except:
            pass
        return False

    def _test_oauth_auth(self, page: Page, issues: List[str], recommendations: List[str]) -> bool:
        """Test OAuth authentication (placeholder for OAuth flows)"""
        issues.append("OAuth testing not yet implemented")
        recommendations.append("Implement OAuth flow testing")
        return False

    def _test_generic_auth(self, page: Page, issues: List[str], recommendations: List[str]) -> bool:
        """Test generic/custom authentication"""
        issues.append("Custom authentication method detected - manual verification needed")
        recommendations.append("Implement custom authentication testing for this application")
        return False
