"""
Universal Workflow Testing Engine

Tests complete user journeys and multi-step workflows across any web application.
Catches workflow navigation bugs, form wizard issues, and user journey failures.

Universal Support:
- Any multi-step workflow (wizards, checkouts, onboarding)
- Any web framework (React, Vue, Angular, vanilla JS)
- Any form library (<PERSON>ik, React Hook Form, Vue Forms, etc.)

Critical Issue Detection:
- Duplicate case statements in switch/if-else logic
- Navigation button failures
- Step validation issues
- State management problems
"""

import time
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from playwright.sync_api import Page, expect

from ..config.test_config import WorkflowConfig
from ..utils.test_result import TestResult, TestSeverity, TestStatus, TestCategory, create_workflow_result


@dataclass
class WorkflowStep:
    """Universal workflow step representation"""
    step_number: int
    step_name: str
    required_fields: List[str] = None
    optional_fields: List[str] = None
    validation_rules: Dict[str, Any] = None
    next_button_selector: str = None
    previous_button_selector: str = None


@dataclass
class WorkflowState:
    """Universal workflow state tracking"""
    current_step: int = 1
    total_steps: int = 0
    completed_steps: List[int] = None
    form_data: Dict[str, Any] = None
    validation_errors: List[str] = None
    navigation_history: List[int] = None
    
    def __post_init__(self):
        if self.completed_steps is None:
            self.completed_steps = []
        if self.form_data is None:
            self.form_data = {}
        if self.validation_errors is None:
            self.validation_errors = []
        if self.navigation_history is None:
            self.navigation_history = []


class WorkflowEngine:
    """
    Universal Workflow Testing Engine
    
    Tests multi-step workflows and user journeys across any web application.
    Detects navigation issues, form validation problems, and state management bugs.
    """
    
    def __init__(self, config: WorkflowConfig, logger: logging.Logger = None):
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        self.workflow_state = WorkflowState()
        self.test_results: List[TestResult] = []
    
    def run_comprehensive_workflow_tests(self, page: Page, workflow_selector: str = None) -> List[TestResult]:
        """
        Run comprehensive workflow testing suite
        
        Universal tests that work with any multi-step workflow:
        1. Workflow detection and analysis
        2. Step navigation testing
        3. Form validation testing
        4. State management validation
        5. Error handling testing
        6. Complete journey validation
        """
        self.logger.info("🔄 Starting comprehensive workflow testing")
        self.test_results = []
        
        # Test 1: Detect and analyze workflow
        workflow_info = self.detect_workflow(page, workflow_selector)
        if not workflow_info:
            self.test_results.append(create_workflow_result(
                "workflow_detection", TestStatus.SKIPPED,
                "No multi-step workflow detected", TestSeverity.LOW,
                ["Add workflow testing if multi-step processes exist"]
            ))
            return self.test_results
        
        # Test 2: Step Navigation
        self.test_step_navigation(page, workflow_info)
        
        # Test 3: Form Validation
        self.test_form_validation(page, workflow_info)
        
        # Test 4: State Management
        self.test_state_management(page, workflow_info)
        
        # Test 5: Error Handling
        self.test_error_handling(page, workflow_info)
        
        # Test 6: Complete Journey
        self.test_complete_journey(page, workflow_info)
        
        # Test 7: Critical Bug Detection (duplicate cases, etc.)
        self.test_critical_workflow_bugs(page, workflow_info)
        
        return self.test_results
    
    def detect_workflow(self, page: Page, workflow_selector: str = None) -> Optional[Dict[str, Any]]:
        """
        Universal workflow detection
        
        Detects multi-step workflows by looking for common patterns:
        - Step indicators (1, 2, 3 or Step 1 of 3)
        - Progress bars
        - Next/Previous buttons
        - Form wizards
        - Multi-page processes
        """
        self.logger.info("Detecting workflow structure")
        
        # Common workflow indicators
        workflow_indicators = [
            # Step indicators
            '.step-indicator', '.steps', '.wizard-steps', '[data-testid*="step"]',
            # Progress indicators
            '.progress', '.progress-bar', '.wizard-progress',
            # Navigation buttons
            'button:has-text("Next")', 'button:has-text("Previous")', 
            'button:has-text("Continue")', 'button:has-text("Back")',
            # Form wizards
            '.wizard', '.form-wizard', '.multi-step-form',
            # Stepper components
            '.stepper', '.step-navigation', '.breadcrumb'
        ]
        
        detected_elements = {}
        for indicator in workflow_indicators:
            elements = page.query_selector_all(indicator)
            if elements:
                detected_elements[indicator] = len(elements)
        
        if not detected_elements:
            return None
        
        # Analyze workflow structure
        workflow_info = {
            'detected_elements': detected_elements,
            'total_steps': self._detect_total_steps(page),
            'current_step': self._detect_current_step(page),
            'navigation_buttons': self._find_navigation_buttons(page),
            'form_elements': self._find_form_elements(page),
            'step_indicators': self._find_step_indicators(page)
        }
        
        self.workflow_state.total_steps = workflow_info['total_steps']
        self.workflow_state.current_step = workflow_info['current_step']
        
        return workflow_info
    
    def test_step_navigation(self, page: Page, workflow_info: Dict[str, Any]) -> TestResult:
        """
        Test step navigation functionality
        
        This is the critical test that catches issues like:
        - Duplicate case statements in switch logic
        - Next button failures on specific steps
        - Navigation state management issues
        """
        self.logger.info("Testing step navigation")
        issues = []
        recommendations = []
        
        try:
            navigation_buttons = workflow_info['navigation_buttons']
            total_steps = workflow_info['total_steps']
            
            # Test forward navigation through all steps
            for step in range(1, total_steps + 1):
                self.logger.info(f"Testing navigation to step {step}")
                
                # Record current state
                current_step_before = self._detect_current_step(page)
                
                # Try to navigate to next step
                next_button = self._find_next_button(page)
                if next_button:
                    # Check if button is enabled
                    if next_button.is_disabled():
                        # This might be expected if form is not valid
                        self.logger.info(f"Next button disabled on step {step}")
                        
                        # Try to fill required fields to enable button
                        self._fill_required_fields(page, step)
                        
                        # Check again
                        if next_button.is_disabled():
                            issues.append(f"Next button remains disabled on step {step} even after filling required fields")
                            recommendations.append(f"Check form validation logic for step {step}")
                            continue
                    
                    # Click next button
                    try:
                        next_button.click()
                        page.wait_for_timeout(self.config.navigation_timeout)
                        
                        # Verify navigation occurred
                        current_step_after = self._detect_current_step(page)
                        
                        if current_step_after == current_step_before:
                            # Navigation failed - this could be the duplicate case bug!
                            issues.append(f"Navigation failed from step {step} - Next button click had no effect")
                            recommendations.append(f"Check switch/case logic for step {step} navigation - possible duplicate case statements")
                            
                            # Try to detect duplicate case bug specifically
                            if self._detect_duplicate_case_bug(page, step):
                                issues.append(f"CRITICAL: Duplicate case statement detected in step {step} navigation logic")
                                recommendations.append(f"Fix duplicate case {step} in switch statement - check renderStepContent or similar navigation logic")
                        
                        elif current_step_after != step + 1 and step < total_steps:
                            issues.append(f"Unexpected navigation from step {step} to step {current_step_after}")
                            recommendations.append(f"Verify step navigation logic for step {step}")
                        
                        self.workflow_state.navigation_history.append(current_step_after)
                        
                    except Exception as e:
                        issues.append(f"Error clicking Next button on step {step}: {str(e)}")
                        recommendations.append(f"Check Next button functionality on step {step}")
                        break
                else:
                    if step < total_steps:
                        issues.append(f"Next button not found on step {step}")
                        recommendations.append(f"Ensure Next button is present and accessible on step {step}")
            
            # Test backward navigation
            self._test_backward_navigation(page, issues, recommendations)
            
        except Exception as e:
            issues.append(f"Step navigation test failed: {str(e)}")
            recommendations.append("Review workflow navigation implementation")
        
        severity = TestSeverity.CRITICAL if any("duplicate case" in issue.lower() for issue in issues) else \
                  TestSeverity.HIGH if any("navigation failed" in issue.lower() for issue in issues) else \
                  TestSeverity.MEDIUM if issues else TestSeverity.LOW
        
        status = TestStatus.FAILED if issues else TestStatus.PASSED
        
        result = create_workflow_result(
            "step_navigation", status,
            "; ".join(issues) if issues else "Step navigation working properly",
            severity, recommendations
        )
        
        self.test_results.append(result)
        return result
    
    def test_critical_workflow_bugs(self, page: Page, workflow_info: Dict[str, Any]) -> TestResult:
        """
        Test for critical workflow bugs that break user journeys
        
        Specifically looks for:
        - Duplicate case statements in navigation logic
        - Switch statement fallthrough issues
        - State management conflicts
        """
        self.logger.info("Testing for critical workflow bugs")
        issues = []
        recommendations = []
        
        try:
            # Test each step for critical navigation bugs
            total_steps = workflow_info['total_steps']
            
            for step in range(1, total_steps + 1):
                # Navigate to specific step
                if not self._navigate_to_step(page, step):
                    continue
                
                # Test for duplicate case bug
                if self._detect_duplicate_case_bug(page, step):
                    issues.append(f"CRITICAL: Duplicate case statement detected in step {step}")
                    recommendations.append(f"Fix duplicate case {step} in switch statement")
                    
                    # Try to provide specific code location
                    code_location = self._identify_code_location(page, step)
                    if code_location:
                        recommendations.append(f"Check {code_location} for duplicate case statements")
                
                # Test for state management conflicts
                state_conflicts = self._detect_state_conflicts(page, step)
                if state_conflicts:
                    issues.extend(state_conflicts)
                    recommendations.append(f"Fix state management conflicts in step {step}")
                
                # Test for navigation logic errors
                nav_errors = self._detect_navigation_logic_errors(page, step)
                if nav_errors:
                    issues.extend(nav_errors)
                    recommendations.append(f"Fix navigation logic errors in step {step}")
        
        except Exception as e:
            issues.append(f"Critical bug detection failed: {str(e)}")
            recommendations.append("Review workflow implementation for critical bugs")
        
        severity = TestSeverity.CRITICAL if any("CRITICAL" in issue for issue in issues) else TestSeverity.LOW
        status = TestStatus.CRITICAL if severity == TestSeverity.CRITICAL else \
                TestStatus.FAILED if issues else TestStatus.PASSED
        
        result = create_workflow_result(
            "critical_workflow_bugs", status,
            "; ".join(issues) if issues else "No critical workflow bugs detected",
            severity, recommendations
        )
        
        self.test_results.append(result)
        return result

    def test_form_validation(self, page: Page, workflow_info: Dict[str, Any]) -> TestResult:
        """Test form validation in workflow steps"""
        self.logger.info("Testing form validation")
        issues = []
        recommendations = []

        try:
            total_steps = workflow_info['total_steps']

            for step in range(1, total_steps + 1):
                if not self._navigate_to_step(page, step):
                    continue

                # Test required field validation
                required_fields = page.query_selector_all('input[required], select[required], textarea[required]')
                for field in required_fields:
                    # Try submitting without filling required field
                    field.fill('')
                    next_button = self._find_next_button(page)
                    if next_button and not next_button.is_disabled():
                        next_button.click()
                        page.wait_for_timeout(1000)

                        # Check if validation error appears
                        error_messages = self._find_validation_errors(page)
                        if not error_messages:
                            field_name = field.get_attribute('name') or 'unknown'
                            issues.append(f"Required field '{field_name}' validation missing on step {step}")
                            recommendations.append(f"Add validation for required field '{field_name}' on step {step}")

        except Exception as e:
            issues.append(f"Form validation test failed: {str(e)}")
            recommendations.append("Review form validation implementation")

        severity = TestSeverity.MEDIUM if issues else TestSeverity.LOW
        status = TestStatus.FAILED if issues else TestStatus.PASSED

        result = create_workflow_result(
            "form_validation", status,
            "; ".join(issues) if issues else "Form validation working properly",
            severity, recommendations
        )

        self.test_results.append(result)
        return result

    def test_state_management(self, page: Page, workflow_info: Dict[str, Any]) -> TestResult:
        """Test workflow state management"""
        self.logger.info("Testing state management")
        issues = []
        recommendations = []

        try:
            # Test state persistence across steps
            initial_data = self._extract_form_data(page)

            # Navigate forward and back
            next_button = self._find_next_button(page)
            if next_button:
                self._fill_required_fields(page, 1)
                next_button.click()
                page.wait_for_timeout(1000)

                prev_button = self._find_previous_button(page)
                if prev_button:
                    prev_button.click()
                    page.wait_for_timeout(1000)

                    # Check if data persisted
                    current_data = self._extract_form_data(page)
                    for key, value in initial_data.items():
                        if key in current_data and current_data[key] != value:
                            issues.append(f"Form data not persisted for field '{key}'")
                            recommendations.append("Implement proper state management for form data")

        except Exception as e:
            issues.append(f"State management test failed: {str(e)}")
            recommendations.append("Review state management implementation")

        severity = TestSeverity.MEDIUM if issues else TestSeverity.LOW
        status = TestStatus.FAILED if issues else TestStatus.PASSED

        result = create_workflow_result(
            "state_management", status,
            "; ".join(issues) if issues else "State management working properly",
            severity, recommendations
        )

        self.test_results.append(result)
        return result

    def test_error_handling(self, page: Page, workflow_info: Dict[str, Any]) -> TestResult:
        """Test error handling in workflow"""
        self.logger.info("Testing error handling")
        issues = []
        recommendations = []

        try:
            # Test network error handling
            # This would require more advanced setup with network interception

            # Test validation error display
            validation_errors = self._find_validation_errors(page)
            if validation_errors:
                # Check if errors are properly styled and accessible
                for error in validation_errors:
                    if not error.is_visible():
                        issues.append("Validation errors not visible to users")
                        recommendations.append("Ensure validation errors are properly displayed")

        except Exception as e:
            issues.append(f"Error handling test failed: {str(e)}")
            recommendations.append("Review error handling implementation")

        severity = TestSeverity.LOW
        status = TestStatus.FAILED if issues else TestStatus.PASSED

        result = create_workflow_result(
            "error_handling", status,
            "; ".join(issues) if issues else "Error handling working properly",
            severity, recommendations
        )

        self.test_results.append(result)
        return result

    def test_complete_journey(self, page: Page, workflow_info: Dict[str, Any]) -> TestResult:
        """Test complete user journey from start to finish"""
        self.logger.info("Testing complete user journey")
        issues = []
        recommendations = []

        try:
            total_steps = workflow_info['total_steps']

            # Navigate through entire workflow
            for step in range(1, total_steps + 1):
                current_step = self._detect_current_step(page)
                if current_step != step:
                    issues.append(f"Expected to be on step {step}, but on step {current_step}")
                    break

                # Fill required fields
                self._fill_required_fields(page, step)

                # Navigate to next step or submit
                if step < total_steps:
                    next_button = self._find_next_button(page)
                    if next_button:
                        next_button.click()
                        page.wait_for_timeout(self.config.navigation_timeout)
                    else:
                        issues.append(f"Next button not found on step {step}")
                        break
                else:
                    # Last step - should have submit button
                    submit_button = page.query_selector(self.config.form_selectors['submit_button'])
                    if submit_button:
                        submit_button.click()
                        page.wait_for_timeout(self.config.form_submission_timeout)

                        # Check for success indication
                        if not self._detect_submission_success(page):
                            issues.append("Form submission did not complete successfully")
                            recommendations.append("Check form submission handling and success feedback")
                    else:
                        issues.append("Submit button not found on final step")
                        recommendations.append("Ensure submit button is present on final step")

        except Exception as e:
            issues.append(f"Complete journey test failed: {str(e)}")
            recommendations.append("Review complete workflow implementation")

        severity = TestSeverity.HIGH if issues else TestSeverity.LOW
        status = TestStatus.FAILED if issues else TestStatus.PASSED

        result = create_workflow_result(
            "complete_journey", status,
            "; ".join(issues) if issues else "Complete user journey successful",
            severity, recommendations
        )

        self.test_results.append(result)
        return result

    def _detect_total_steps(self, page: Page) -> int:
        """Detect total number of steps in workflow"""
        # Look for step indicators
        step_indicators = [
            '.step-indicator', '.steps li', '.wizard-step',
            '[data-testid*="step"]', '.stepper-item'
        ]

        max_steps = 0
        for selector in step_indicators:
            elements = page.query_selector_all(selector)
            if elements:
                max_steps = max(max_steps, len(elements))

        # Look for "Step X of Y" text
        step_text_elements = page.query_selector_all('text=/Step \\d+ of (\\d+)/')
        for element in step_text_elements:
            text = element.text_content()
            if text and 'of' in text:
                try:
                    total = int(text.split('of')[-1].strip())
                    max_steps = max(max_steps, total)
                except:
                    pass

        return max_steps if max_steps > 0 else 1

    def _detect_current_step(self, page: Page) -> int:
        """Detect current step number"""
        # Look for active step indicators
        active_selectors = [
            '.step.active', '.step-active', '.current-step',
            '.step.current', '[data-current="true"]'
        ]

        for selector in active_selectors:
            element = page.query_selector(selector)
            if element:
                # Try to extract step number
                text = element.text_content()
                if text:
                    try:
                        return int(''.join(filter(str.isdigit, text)))
                    except:
                        pass

        # Look for "Step X" text
        step_text = page.query_selector('text=/Step (\\d+)/')
        if step_text:
            text = step_text.text_content()
            try:
                return int(''.join(filter(str.isdigit, text)))
            except:
                pass

        return 1

    def _find_navigation_buttons(self, page: Page) -> Dict[str, Any]:
        """Find navigation buttons"""
        return {
            'next': self._find_next_button(page),
            'previous': self._find_previous_button(page),
            'submit': page.query_selector(self.config.form_selectors['submit_button']),
            'cancel': page.query_selector(self.config.form_selectors['cancel_button'])
        }

    def _find_next_button(self, page: Page) -> Optional[Any]:
        """Find Next button with universal selectors"""
        selectors = [
            self.config.form_selectors['next_button'],
            'button:has-text("Next")', 'button:has-text("Continue")',
            '.next-btn', '.btn-next', '[data-testid="next"]',
            'input[type="submit"][value*="Next"]'
        ]

        for selector in selectors:
            button = page.query_selector(selector)
            if button:
                return button
        return None

    def _find_previous_button(self, page: Page) -> Optional[Any]:
        """Find Previous button with universal selectors"""
        selectors = [
            self.config.form_selectors['previous_button'],
            'button:has-text("Previous")', 'button:has-text("Back")',
            '.prev-btn', '.btn-prev', '[data-testid="previous"]',
            'input[type="button"][value*="Previous"]'
        ]

        for selector in selectors:
            button = page.query_selector(selector)
            if button:
                return button
        return None

    def _find_form_elements(self, page: Page) -> Dict[str, List]:
        """Find form elements in current step"""
        return {
            'inputs': page.query_selector_all('input'),
            'selects': page.query_selector_all('select'),
            'textareas': page.query_selector_all('textarea'),
            'checkboxes': page.query_selector_all('input[type="checkbox"]'),
            'radios': page.query_selector_all('input[type="radio"]')
        }

    def _find_step_indicators(self, page: Page) -> List[Any]:
        """Find step indicator elements"""
        selectors = [
            '.step-indicator', '.steps li', '.wizard-step',
            '.stepper-item', '[data-testid*="step"]'
        ]

        indicators = []
        for selector in selectors:
            elements = page.query_selector_all(selector)
            indicators.extend(elements)

        return indicators

    def _fill_required_fields(self, page: Page, step: int):
        """Fill required fields to enable navigation"""
        # Find required fields
        required_fields = page.query_selector_all('input[required], select[required], textarea[required]')
        required_fields.extend(page.query_selector_all('[aria-required="true"]'))

        for field in required_fields:
            try:
                field_type = field.get_attribute('type') or 'text'
                field_name = field.get_attribute('name') or f'field_{step}'

                if field_type == 'email':
                    field.fill('<EMAIL>')
                elif field_type == 'password':
                    field.fill('TestPassword123!')
                elif field_type == 'text':
                    field.fill(f'Test Value {step}')
                elif field_type == 'number':
                    field.fill('123')
                elif field.tag_name.lower() == 'select':
                    options = field.query_selector_all('option')
                    if len(options) > 1:
                        field.select_option(index=1)
                elif field_type == 'checkbox':
                    field.check()
                elif field_type == 'radio':
                    field.check()

            except Exception as e:
                self.logger.debug(f"Could not fill field: {e}")

    def _find_validation_errors(self, page: Page) -> List[Any]:
        """Find validation error messages"""
        error_selectors = self.config.error_message_selectors
        errors = []

        for selector in error_selectors:
            elements = page.query_selector_all(selector)
            errors.extend(elements)

        return errors

    def _detect_submission_success(self, page: Page) -> bool:
        """Detect successful form submission"""
        success_indicators = [
            'text="Success"', 'text="Thank you"', 'text="Completed"',
            '.success', '.success-message', '[data-testid="success"]',
            'text="Submitted"', 'text="Created"'
        ]

        for indicator in success_indicators:
            if page.query_selector(indicator):
                return True

        # Check if URL changed (redirect after submission)
        if 'success' in page.url.lower() or 'thank' in page.url.lower():
            return True

        return False

    def _test_backward_navigation(self, page: Page, issues: List[str], recommendations: List[str]):
        """Test backward navigation through workflow"""
        try:
            current_step = self._detect_current_step(page)

            while current_step > 1:
                prev_button = self._find_previous_button(page)
                if not prev_button:
                    issues.append(f"Previous button not found on step {current_step}")
                    recommendations.append(f"Add Previous button on step {current_step}")
                    break

                prev_button.click()
                page.wait_for_timeout(self.config.navigation_timeout)

                new_step = self._detect_current_step(page)
                if new_step >= current_step:
                    issues.append(f"Backward navigation failed from step {current_step}")
                    recommendations.append(f"Fix Previous button functionality on step {current_step}")
                    break

                current_step = new_step

        except Exception as e:
            issues.append(f"Backward navigation test failed: {str(e)}")
            recommendations.append("Review backward navigation implementation")

    def _detect_duplicate_case_bug(self, page: Page, step: int) -> bool:
        """
        Detect duplicate case statements in navigation logic

        This is the specific bug we found in InterviewConfigurationWizard.tsx
        where duplicate case 3/case 4 statements broke navigation.
        """
        try:
            # Record initial state
            initial_step = self._detect_current_step(page)

            # Try to navigate multiple times
            next_button = self._find_next_button(page)
            if not next_button:
                return False

            # Fill any required fields first
            self._fill_required_fields(page, step)

            # Click next button multiple times and check if step changes
            click_results = []
            for i in range(3):
                if next_button.is_disabled():
                    break

                next_button.click()
                page.wait_for_timeout(1000)
                current_step = self._detect_current_step(page)
                click_results.append(current_step)

            # If step never changes despite clicking, likely duplicate case bug
            if len(set(click_results)) == 1 and click_results[0] == initial_step:
                return True

        except Exception as e:
            self.logger.debug(f"Duplicate case detection failed: {e}")

        return False

    def _navigate_to_step(self, page: Page, target_step: int) -> bool:
        """Navigate to a specific step"""
        current_step = self._detect_current_step(page)

        while current_step < target_step:
            next_button = self._find_next_button(page)
            if not next_button:
                return False

            self._fill_required_fields(page, current_step)
            next_button.click()
            page.wait_for_timeout(self.config.navigation_timeout)

            new_step = self._detect_current_step(page)
            if new_step == current_step:
                return False  # Navigation failed
            current_step = new_step

        return current_step == target_step

    def _detect_state_conflicts(self, page: Page, step: int) -> List[str]:
        """Detect state management conflicts"""
        conflicts = []

        # Check for conflicting step indicators
        active_steps = page.query_selector_all('.step.active, .step-active, .current-step')
        if len(active_steps) > 1:
            conflicts.append(f"Multiple active step indicators on step {step}")

        # Check for form state conflicts
        form_data = self._extract_form_data(page)
        if self.workflow_state.form_data:
            for key, value in form_data.items():
                if key in self.workflow_state.form_data and self.workflow_state.form_data[key] != value:
                    conflicts.append(f"Form data conflict for field {key} on step {step}")

        return conflicts

    def _detect_navigation_logic_errors(self, page: Page, step: int) -> List[str]:
        """Detect navigation logic errors"""
        errors = []

        # Check if navigation buttons have proper state
        next_button = self._find_next_button(page)
        prev_button = self._find_previous_button(page)

        if step == 1 and prev_button and not prev_button.is_disabled():
            errors.append(f"Previous button should be disabled on first step")

        if step == self.workflow_state.total_steps and next_button:
            button_text = next_button.text_content().lower()
            if 'next' in button_text and 'submit' not in button_text and 'finish' not in button_text:
                errors.append(f"Next button should be Submit/Finish button on last step")

        return errors

    def _extract_form_data(self, page: Page) -> Dict[str, Any]:
        """Extract current form data"""
        form_data = {}

        # Extract input values
        inputs = page.query_selector_all('input')
        for input_elem in inputs:
            name = input_elem.get_attribute('name')
            if name:
                input_type = input_elem.get_attribute('type') or 'text'
                if input_type in ['checkbox', 'radio']:
                    form_data[name] = input_elem.is_checked()
                else:
                    form_data[name] = input_elem.input_value()

        # Extract select values
        selects = page.query_selector_all('select')
        for select_elem in selects:
            name = select_elem.get_attribute('name')
            if name:
                form_data[name] = select_elem.input_value()

        return form_data

    def _identify_code_location(self, page: Page, step: int) -> Optional[str]:
        """Try to identify code location for debugging"""
        # Look for React component names in data attributes
        react_components = page.query_selector_all('[data-reactroot] *')
        for component in react_components:
            class_name = component.get_attribute('class')
            if class_name and 'wizard' in class_name.lower():
                return f"React component with class: {class_name}"

        # Look for common file patterns
        if page.query_selector('.interview-wizard, .interview-configuration'):
            return "InterviewConfigurationWizard.tsx or similar"

        return None
