import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { createSuccessResponse, ErrorResponses, withErrorHandling, createPaginationMeta } from '@/lib/api-response';
import { validateInput, paginationSchema, resourceFilterSchema } from '@/lib/validation';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';
import { apiCache } from '@/lib/cache';

export const GET = withErrorHandling(async (request: NextRequest) => {
  // Apply rate limiting
  const rateLimitResult = withRateLimit(rateLimiters.search)(request);
  if (!rateLimitResult.allowed) {
    return ErrorResponses.tooManyRequests();
  }

  const { searchParams } = new URL(request.url);

  // Create cache key from search params
  const cacheKey = `learning_resources:${searchParams.toString()}`;

  // Check cache first
  const cached = apiCache.getJSON(cacheKey);
  if (cached) {
    return NextResponse.json({
      success: true,
      data: cached.data,
      meta: cached.meta,
      cached: true
    });
  }

  // Validate pagination parameters
  const paginationValidation = paginationSchema.safeParse({
    page: searchParams.get('page'),
    limit: searchParams.get('limit'),
  });

  if (!paginationValidation.success) {
    return ErrorResponses.validationError([paginationValidation.error.message]);
  }

  const { page = 1, limit = 10 } = paginationValidation.data;

  // Validate filter parameters
  const filterValidation = resourceFilterSchema.safeParse({
    category: searchParams.get('category'),
    skillLevel: searchParams.get('skillLevel'),
    type: searchParams.get('type'),
    cost: searchParams.get('cost'),
    search: searchParams.get('search'),
  });

  if (!filterValidation.success) {
    return ErrorResponses.validationError([filterValidation.error.message]);
  }

  const { category, skillLevel, type, cost, search } = filterValidation.data;

  const where: Record<string, unknown> = {
    isActive: true,
  };

  if (category && category !== 'all') {
    where.category = category.toUpperCase();
  }

  if (skillLevel && skillLevel !== 'all') {
    where.skillLevel = skillLevel.toUpperCase();
  }

  if (type && type !== 'all') {
    where.type = type.toUpperCase();
  }

  if (cost && cost !== 'all') {
    where.cost = cost.toUpperCase();
  }

  if (search) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
      { author: { contains: search, mode: 'insensitive' } },
    ];
  }

  // Simplified query for better performance - get basic data first
  const [total, resources] = await Promise.all([
    prisma.learningResource.count({ where }),
    prisma.learningResource.findMany({
      where,
      skip: (page - 1) * limit,
      take: limit,
      orderBy: [
        { category: 'asc' },
        { skillLevel: 'asc' },
        { title: 'asc' }
      ],
      // Minimal includes for better performance
      select: {
        id: true,
        title: true,
        description: true,
        url: true,
        type: true,
        category: true,
        skillLevel: true,
        author: true,
        duration: true,
        cost: true,
        format: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        careerPaths: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        skills: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })
  ]);

  // Add default rating values (optimize this later if needed)
  const resourcesWithRatings = resources.map(resource => ({
    ...resource,
    averageRating: 0,
    totalRatings: 0
  }));

  const meta = createPaginationMeta(page, limit, total);
  const result = { data: resourcesWithRatings, meta };

  // Cache the result for 5 minutes
  apiCache.setJSON(cacheKey, result, 5 * 60 * 1000);

  return createSuccessResponse(resourcesWithRatings, undefined, meta);
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      title,
      description,
      url,
      type,
      category,
      skillLevel,
      author,
      duration,
      cost = 'FREE',
      format
    } = body;

    // Validate required fields
    if (!title || !description || !url || !type || !category || !skillLevel || !format) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields' 
        },
        { status: 400 }
      );
    }

    const resource = await prisma.learningResource.create({
      data: {
        title,
        description,
        url,
        type: type.toUpperCase(),
        category: category.toUpperCase(),
        skillLevel: skillLevel.toUpperCase(),
        author,
        duration,
        cost: cost.toUpperCase(),
        format: format.toUpperCase()
      }
    });

    return NextResponse.json({
      success: true,
      data: resource
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating learning resource:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create learning resource' 
      },
      { status: 500 }
    );
  }
}
