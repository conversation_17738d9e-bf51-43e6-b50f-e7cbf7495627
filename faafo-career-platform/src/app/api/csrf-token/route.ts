import { NextRequest, NextResponse } from 'next/server';
import { SimpleSecurity } from '@/lib/simple-security';

export async function GET(request: NextRequest) {
  try {
    const csrfToken = SimpleSecurity.generateCSRFToken();

    const response = NextResponse.json(
      {
        csrfToken,
        timestamp: Date.now()
      },
      {
        status: 200,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
          'Pragma': 'no-cache'
        }
      }
    );

    return SimpleSecurity.addSecurityHeaders(response);
  } catch (error) {
    console.error('CSRF token generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate CSRF token' },
      { status: 500 }
    );
  }
}
