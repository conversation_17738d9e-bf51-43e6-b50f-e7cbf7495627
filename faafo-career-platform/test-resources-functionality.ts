#!/usr/bin/env node

/**
 * Comprehensive Resources Tab Testing Script
 * Tests all functionality and identifies flaws
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3003';

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
}

const results: TestResult[] = [];

function addResult(test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any) {
  results.push({ test, status, message, details });
  const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  console.log(`${emoji} ${test}: ${message}`);
  if (details) {
    console.log(`   Details:`, details);
  }
}

async function testAPI(endpoint: string, expectedStatus = 200) {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`);
    const data = await response.json();
    
    if (response.status === expectedStatus) {
      return { success: true, data, status: response.status };
    } else {
      return { success: false, data, status: response.status };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function runTests() {
  console.log('🔍 Starting comprehensive Resources Tab testing...\n');

  // Test 1: Database Resources API
  console.log('📊 Testing Database Resources...');
  const dbResourcesTest = await testAPI('/api/learning-resources?limit=100');
  if (dbResourcesTest.success && dbResourcesTest.data.success) {
    const count = dbResourcesTest.data.meta.total;
    addResult('Database Resources API', 'PASS', `Found ${count} database resources`);
    
    if (count === 0) {
      addResult('Database Resources Count', 'FAIL', 'No resources in database - seeding failed');
    } else if (count < 50) {
      addResult('Database Resources Count', 'WARNING', `Only ${count} resources - expected more`);
    } else {
      addResult('Database Resources Count', 'PASS', `Good resource count: ${count}`);
    }
  } else {
    addResult('Database Resources API', 'FAIL', 'API call failed', dbResourcesTest);
  }

  // Test 2: Individual Resource Detail API (Database Resource)
  console.log('\n🔍 Testing Individual Resource Details...');
  if (dbResourcesTest.success && dbResourcesTest.data.data.length > 0) {
    const firstResource = dbResourcesTest.data.data[0];
    const detailTest = await testAPI(`/api/learning-resources/${firstResource.id}`);
    
    if (detailTest.success && detailTest.data.success) {
      addResult('Database Resource Detail API', 'PASS', `Successfully fetched resource: ${firstResource.title}`);
    } else {
      addResult('Database Resource Detail API', 'FAIL', 'Failed to fetch database resource details', detailTest);
    }
  }

  // Test 3: Static Resource Detail API (Should fail)
  console.log('\n🔍 Testing Static Resource Details...');
  const staticResourceTest = await testAPI('/api/learning-resources/1', 404);
  if (staticResourceTest.status === 404) {
    addResult('Static Resource Detail API', 'FAIL', 'Static resources not accessible via API - CRITICAL FLAW');
  } else {
    addResult('Static Resource Detail API', 'WARNING', 'Unexpected response for static resource');
  }

  // Test 4: Resource Progress API
  console.log('\n📈 Testing Progress Tracking...');
  const progressTest = await testAPI('/api/learning-progress', 401); // Should require auth
  if (progressTest.status === 401) {
    addResult('Progress API Authentication', 'PASS', 'Properly requires authentication');
  } else {
    addResult('Progress API Authentication', 'FAIL', 'Authentication not working properly');
  }

  // Test 5: Resource Ratings API
  console.log('\n⭐ Testing Rating System...');
  const ratingsTest = await testAPI('/api/resource-ratings?resourceId=test', 400); // Should require valid resource
  if (ratingsTest.status === 400) {
    addResult('Ratings API Validation', 'PASS', 'Properly validates resource ID');
  } else {
    addResult('Ratings API Validation', 'WARNING', 'Unexpected ratings API behavior');
  }

  // Test 6: Resource Categories
  console.log('\n📂 Testing Resource Categories...');
  if (dbResourcesTest.success && dbResourcesTest.data.data.length > 0) {
    const categories = [...new Set(dbResourcesTest.data.data.map((r: any) => r.category))];
    addResult('Resource Categories', 'PASS', `Found ${categories.length} categories: ${categories.join(', ')}`);
    
    // Check for expected categories
    const expectedCategories = ['CYBERSECURITY', 'DATA_SCIENCE', 'ARTIFICIAL_INTELLIGENCE'];
    const missingCategories = expectedCategories.filter(cat => !categories.includes(cat));
    if (missingCategories.length > 0) {
      addResult('Expected Categories', 'WARNING', `Missing categories: ${missingCategories.join(', ')}`);
    } else {
      addResult('Expected Categories', 'PASS', 'All expected categories present');
    }
  }

  // Test 7: Resource Types and Formats
  console.log('\n📝 Testing Resource Types...');
  if (dbResourcesTest.success && dbResourcesTest.data.data.length > 0) {
    const types = [...new Set(dbResourcesTest.data.data.map((r: any) => r.type))];
    const formats = [...new Set(dbResourcesTest.data.data.map((r: any) => r.format))];
    addResult('Resource Types', 'PASS', `Found types: ${types.join(', ')}`);
    addResult('Resource Formats', 'PASS', `Found formats: ${formats.join(', ')}`);
  }

  // Test 8: Career Path Connections
  console.log('\n🛤️ Testing Career Path Connections...');
  if (dbResourcesTest.success && dbResourcesTest.data.data.length > 0) {
    const resourcesWithPaths = dbResourcesTest.data.data.filter((r: any) => r.careerPaths && r.careerPaths.length > 0);
    if (resourcesWithPaths.length === 0) {
      addResult('Career Path Connections', 'FAIL', 'No resources connected to career paths - CRITICAL FLAW');
    } else {
      addResult('Career Path Connections', 'PASS', `${resourcesWithPaths.length} resources connected to career paths`);
    }
  }

  // Summary
  console.log('\n📊 TEST SUMMARY');
  console.log('================');
  
  const passed = results.filter(r => r.status === 'PASS').length;
  const failed = results.filter(r => r.status === 'FAIL').length;
  const warnings = results.filter(r => r.status === 'WARNING').length;
  
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`⚠️ Warnings: ${warnings}`);
  console.log(`📊 Total Tests: ${results.length}`);
  
  if (failed > 0) {
    console.log('\n🚨 CRITICAL ISSUES FOUND:');
    results.filter(r => r.status === 'FAIL').forEach(r => {
      console.log(`   • ${r.test}: ${r.message}`);
    });
  }
  
  if (warnings > 0) {
    console.log('\n⚠️ WARNINGS:');
    results.filter(r => r.status === 'WARNING').forEach(r => {
      console.log(`   • ${r.test}: ${r.message}`);
    });
  }
}

runTests().catch(console.error);
